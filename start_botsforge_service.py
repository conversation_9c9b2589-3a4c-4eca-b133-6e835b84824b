#!/usr/bin/env python3
"""
Script to start the BotsForge CloudFlare Turnstile solving service
"""
import subprocess
import sys
import os
import time

def start_botsforge_service():
    """Start the BotsForge service"""
    print("🚀 Starting BotsForge CloudFlare Turnstile Service...")
    
    # Change to BotsForge directory
    botsforge_dir = "BotsForge-CloudFlare"
    
    if not os.path.exists(botsforge_dir):
        print(f"❌ BotsForge directory not found: {botsforge_dir}")
        print("Please run the setup first!")
        return False
    
    # Check if .env file exists
    env_file = os.path.join(botsforge_dir, ".env")
    if not os.path.exists(env_file):
        print("❌ .env file not found. Creating default configuration...")
        with open(env_file, 'w') as f:
            f.write("max_workers=2\n")
            f.write("PORT=5033\n")
            f.write("API_KEY=pirateship-turnstile-solver-2024\n")
            f.write("LOG_LEVEL=INFO\n")
        print("✅ Created .env file")
    
    # Start the service
    try:
        print("🔧 Starting BotsForge service on port 5033...")
        print("📋 Service will be available at: http://localhost:5033")
        print("🔑 API Key: pirateship-turnstile-solver-2024")
        print("=" * 50)
        
        # Change to the BotsForge directory and run the service
        os.chdir(botsforge_dir)
        
        # Run the service
        subprocess.run([sys.executable, "app.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Service stopped by user")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting service: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🔥 BotsForge CloudFlare Turnstile Service Starter")
    print("=" * 50)
    
    success = start_botsforge_service()
    
    if success:
        print("✅ Service started successfully!")
    else:
        print("❌ Failed to start service!")
        sys.exit(1)
