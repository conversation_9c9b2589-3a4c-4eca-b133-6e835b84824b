# BotsForge Direct Turnstile Integration Guide

This guide shows how to integrate the BotsForge-CloudFlare library's core solving capabilities directly into your automation workflow, bypassing the API layer for better performance and control.

## Overview

The integration provides three main components:

1. **DirectTurnstileSolver**: Core solving engine extracted from BotsForge
2. **TurnstileIntegration**: High-level wrapper for easy integration
3. **Enhanced Integration Functions**: Ready-to-use functions for common scenarios

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements_integration.txt
```

### 2. Install Playwright Browsers

```bash
patchright install chromium
```

### 3. Setup Checkbox Template (Optional)

Copy the `screens/checkbox.png` from BotsForge-CloudFlare directory to enable visual checkbox detection:

```bash
mkdir -p screens
cp BotsForge-CloudFlare/screens/checkbox.png screens/
```

## Usage Examples

### Quick Integration with Existing Selenium Workflow

```python
import asyncio
from turnstile_integration import enhanced_turnstile_solver

async def your_existing_automation():
    # Your existing Selenium setup
    driver = webdriver.Chrome()
    await driver.get("https://your-site.com/login")
    
    # Fill credentials
    await driver.find_element(By.ID, "email").send_keys("<EMAIL>")
    await driver.find_element(By.ID, "password").send_keys("password")
    
    # Solve Turnstile automatically
    success = await enhanced_turnstile_solver(driver)
    
    if success:
        # Continue with login
        await driver.find_element(By.CSS_SELECTOR, "button[type='submit']").click()
    
    return success
```

### Standalone Turnstile Solving

```python
from turnstile_integration import solve_turnstile_quick

async def solve_standalone():
    token = await solve_turnstile_quick(
        website_url="https://example.com",
        website_key="0x4AAAAAAAc6HG1RMG_8EHSC",
        headless=False  # Set True for production
    )
    
    if token:
        print(f"Solved! Token: {token}")
        return token
    else:
        print("Failed to solve")
        return None
```

### Integration with Existing Playwright Page

```python
from turnstile_integration import TurnstileIntegration

async def solve_on_existing_page(page):
    async with TurnstileIntegration() as integration:
        # Extract Turnstile parameters from page
        website_key = await page.get_attribute("#cf-turnstile", "data-sitekey")
        
        # Solve on the existing page
        token = await integration.solve_turnstile_on_page(page, website_key)
        
        if token:
            # Inject token and trigger callbacks
            await integration.inject_token_and_continue(page, token)
            return True
    
    return False
```

## Advanced Usage

### Custom Configuration

```python
from turnstile_integration import TurnstileIntegration

async def custom_solving():
    # Initialize with custom settings
    async with TurnstileIntegration(
        headless=True,
        proxy="username:<EMAIL>:8080"
    ) as integration:
        
        token = await integration.solve_turnstile_standalone(
            website_url="https://example.com",
            website_key="your-site-key",
            action="login"  # Optional action parameter
        )
        
        return token
```

### Batch Solving

```python
async def solve_multiple_sites():
    sites = [
        {"url": "https://site1.com", "key": "key1"},
        {"url": "https://site2.com", "key": "key2"},
    ]
    
    async with TurnstileIntegration(headless=True) as integration:
        results = []
        for site in sites:
            token = await integration.solve_turnstile_standalone(
                website_url=site["url"],
                website_key=site["key"]
            )
            results.append({"site": site["url"], "token": token})
        
        return results
```

## Integration with Your PirateShip Workflow

Here's how to integrate with your existing PirateShip automation:

```python
async def pirateship_login_with_direct_solving():
    # Your existing setup
    driver = webdriver.Chrome()
    await driver.get("https://ship.pirateship.com/login")
    
    # Fill credentials
    await driver.find_element(By.CSS_SELECTOR, "input[type='email']").send_keys(email)
    await driver.find_element(By.CSS_SELECTOR, "input[type='password']").send_keys(password)
    
    # Use enhanced solver (automatically detects and solves Turnstile)
    turnstile_solved = await enhanced_turnstile_solver(
        driver_or_page=driver,
        website_url="https://ship.pirateship.com/login"
    )
    
    if turnstile_solved:
        print("✅ Turnstile solved - proceeding with login")
        
        # Wait for button to be enabled
        await driver.sleep(2)
        
        # Click login
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
        await login_button.click()
        
        # Wait for navigation
        await driver.sleep(5)
        
        # Check success
        if "dashboard" in await driver.current_url:
            print("🎉 Login successful!")
            return True
    
    return False
```

## Key Advantages

1. **No External API Dependency**: Runs completely locally
2. **Better Performance**: Direct integration without HTTP overhead
3. **Full Control**: Access to all solving parameters and methods
4. **Seamless Integration**: Works with existing Selenium/Playwright workflows
5. **Visual Recognition**: Uses OpenCV for reliable checkbox detection
6. **Async Support**: Fully asynchronous for better performance

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure BotsForge-CloudFlare is in your Python path
2. **Browser Issues**: Make sure Playwright browsers are installed with `patchright install chromium`
3. **Checkbox Detection**: Copy the checkbox template image to `screens/checkbox.png`
4. **Token Injection**: Some sites may require specific callback function names

### Debug Mode

Enable debug logging to see detailed solving process:

```python
import logging
from loguru import logger

logger.add("turnstile_debug.log", level="DEBUG")
```

### Performance Tips

1. Use `headless=True` for production
2. Reuse TurnstileIntegration instances when solving multiple Turnstiles
3. Set appropriate timeouts based on your needs
4. Use proxy rotation for high-volume solving

## Migration from API-based Approach

If you're currently using the BotsForge API, here's how to migrate:

### Before (API-based):
```python
# Old API approach
token = await solve_with_botsforge_service(website_url, website_key)
```

### After (Direct integration):
```python
# New direct approach
token = await solve_turnstile_quick(website_url, website_key)
```

The direct approach provides the same functionality with better performance and no external dependencies.

## Support

For issues specific to the BotsForge library, refer to their documentation.
For integration issues, check the example files and ensure all dependencies are properly installed.
