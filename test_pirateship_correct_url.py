"""
Test PirateShip integration with the correct URL
"""

import asyncio
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
from simple_turnstile_integration import solve_turnstile_simple, inject_turnstile_token
from loguru import logger

# Test credentials
email = "<EMAIL>"
password = "sacramento209%%"
element_timeout = 15


async def test_pirateship_with_correct_url():
    """
    Test PirateShip login with the correct URL and proper Turnstile handling
    """
    logger.info("🚀 Testing PirateShip with correct URL: https://ship.pirateship.com/")
    
    options = webdriver.ChromeOptions()
    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    options.add_argument("--disable-blink-features=AutomationControlled")
    
    async with webdriver.Chrome(options=options) as driver:
        try:
            # Navigate to the correct PirateShip URL
            logger.info("🌐 Navigating to https://ship.pirateship.com/")
            await driver.get("https://ship.pirateship.com/")
            await driver.sleep(3)
            
            # Check initial page state
            page_info = await driver.execute_script("""
            return {
                title: document.title,
                url: window.location.href,
                hasEmailInput: !!document.querySelector('input[type="email"]'),
                hasPasswordInput: !!document.querySelector('input[type="password"]'),
                hasLoginButton: !!document.querySelector('button[type="submit"]'),
                hasTurnstile: !!document.querySelector('#cf-turnstile'),
                turnstileKey: document.querySelector('#cf-turnstile')?.getAttribute('data-sitekey'),
                loginButtonDisabled: !!document.querySelector('button[type="submit"]')?.disabled
            };
            """)
            
            logger.info(f"📊 Initial page state: {page_info}")
            
            if not page_info.get('hasEmailInput'):
                logger.error("❌ No email input found on page")
                return False
            
            # Fill in credentials
            logger.info("📝 Filling login credentials")
            email_field = await driver.find_element(By.CSS_SELECTOR, "input[type='email']", timeout=element_timeout)
            await email_field.send_keys(email)
            
            password_field = await driver.find_element(By.CSS_SELECTOR, "input[type='password']", timeout=element_timeout)
            await password_field.send_keys(password)
            
            # Check login button state before clicking
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
            is_disabled_before = await login_button.get_dom_attribute('disabled')
            logger.info(f"🔘 Login button disabled before click: {is_disabled_before is not None}")
            
            # Click login button to trigger Turnstile
            logger.info("🖱️ Clicking login button to trigger Turnstile")
            await login_button.click()
            await driver.sleep(3)
            
            # Check for Turnstile after clicking
            turnstile_info = await driver.execute_script("""
            return {
                hasTurnstile: !!document.querySelector('#cf-turnstile'),
                websiteKey: document.querySelector('#cf-turnstile')?.getAttribute('data-sitekey'),
                dataAction: document.querySelector('#cf-turnstile')?.getAttribute('data-action'),
                hasResponseInput: !!document.querySelector('input[name="cf-turnstile-response"]'),
                loginButtonDisabled: !!document.querySelector('button[type="submit"]')?.disabled,
                iframeCount: document.querySelectorAll('#cf-turnstile iframe').length,
                allSiteKeys: Array.from(document.querySelectorAll('[data-sitekey]')).map(el => el.getAttribute('data-sitekey')),
                currentURL: window.location.href
            };
            """)
            
            logger.info(f"🛡️ Turnstile info after login click: {turnstile_info}")
            
            if turnstile_info.get('hasTurnstile') and turnstile_info.get('websiteKey'):
                logger.info(f"🎯 Turnstile detected with key: {turnstile_info['websiteKey']}")
                
                # Solve the Turnstile using our working integration
                logger.info("🔧 Solving Turnstile with BotsForge integration...")
                token = await solve_turnstile_simple(
                    website_url=turnstile_info['currentURL'],
                    website_key=turnstile_info['websiteKey'],
                    action=turnstile_info.get('dataAction', '')
                )
                
                if token:
                    logger.info(f"✅ Token obtained: {token[:50]}...")
                    
                    # Inject the token
                    logger.info("💉 Injecting token into page...")
                    injection_success = await inject_turnstile_token(driver, token)
                    
                    if injection_success:
                        logger.info("✅ Token injection successful")
                        
                        # Wait for page to process the token
                        await driver.sleep(3)
                        
                        # Check login button state after token injection
                        is_disabled_after = await login_button.get_dom_attribute('disabled')
                        logger.info(f"🔘 Login button disabled after token: {is_disabled_after is not None}")
                        
                        if is_disabled_after is None:
                            logger.info("🎉 Login button enabled - attempting final login")
                            
                            # Click login button again
                            await login_button.click()
                            await driver.sleep(5)
                            
                            # Check for successful login
                            final_url = await driver.current_url
                            logger.info(f"📍 Final URL: {final_url}")
                            
                            if "dashboard" in final_url or ("ship.pirateship.com" in final_url and "login" not in final_url):
                                logger.info("🎉 LOGIN SUCCESSFUL!")
                                return True
                            else:
                                # Check for error messages
                                try:
                                    error_check = await driver.execute_script("""
                                    return {
                                        errors: Array.from(document.querySelectorAll('.error, .alert-danger, [role="alert"]')).map(el => el.innerText),
                                        stillOnLoginPage: window.location.href.includes('ship.pirateship.com') && !window.location.href.includes('dashboard')
                                    };
                                    """)
                                    
                                    if error_check.get('errors'):
                                        logger.warning(f"⚠️ Login errors: {error_check['errors']}")
                                    
                                    if not error_check.get('stillOnLoginPage'):
                                        logger.info("✅ Navigated away from login page - likely successful")
                                        return True
                                    else:
                                        logger.warning("⚠️ Still on login page - login may have failed")
                                        return False
                                        
                                except Exception as e:
                                    logger.warning(f"⚠️ Could not check for errors: {e}")
                                    return False
                        else:
                            logger.warning("⚠️ Login button still disabled after token injection")
                            return False
                    else:
                        logger.error("❌ Token injection failed")
                        return False
                else:
                    logger.error("❌ Failed to obtain Turnstile token")
                    return False
            else:
                logger.warning("⚠️ No Turnstile found or no website key")
                
                # Check if login is possible without Turnstile
                is_disabled = await login_button.get_dom_attribute('disabled')
                if is_disabled is None:
                    logger.info("✅ Login button enabled without Turnstile - attempting login")
                    
                    await login_button.click()
                    await driver.sleep(5)
                    
                    final_url = await driver.current_url
                    if "dashboard" in final_url or ("ship.pirateship.com" in final_url and "login" not in final_url):
                        logger.info("🎉 LOGIN SUCCESSFUL without Turnstile!")
                        return True
                    else:
                        logger.warning("⚠️ Login failed even without Turnstile")
                        return False
                else:
                    logger.warning("⚠️ Login button disabled but no Turnstile found")
                    return False
            
        except Exception as e:
            logger.error(f"❌ Test failed with error: {e}")
            return False


if __name__ == "__main__":
    async def main():
        logger.info("🎯 Testing PirateShip with Correct URL and BotsForge Integration")
        logger.info("=" * 70)
        
        result = await test_pirateship_with_correct_url()
        
        logger.info("\n" + "=" * 70)
        logger.info("📊 FINAL RESULT")
        logger.info("=" * 70)
        
        if result:
            logger.info("🎉 SUCCESS! PirateShip login with Turnstile solving works!")
            logger.info("✅ You can now use the updated PirateApi.py with confidence.")
            logger.info("🔧 Make sure to use the correct URL: https://ship.pirateship.com/")
        else:
            logger.info("❌ Test failed. Check the logs above for details.")
            logger.info("🔧 The integration may need further refinement.")
        
        return result
    
    # Run test
    result = asyncio.run(main())
    print(f"\n🏁 Final Result: {'SUCCESS' if result else 'FAILED'}")
