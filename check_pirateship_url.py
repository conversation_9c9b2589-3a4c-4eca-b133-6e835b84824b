"""
Check PirateShip URLs to find the correct login page
"""

import asyncio
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
from loguru import logger

async def check_pirateship_urls():
    """Check various PirateShip URLs to find the correct login page"""
    
    urls_to_check = [
        "https://ship.pirateship.com/login",
        "https://ship.pirateship.com/",
        "https://www.pirateship.com/login",
        "https://www.pirateship.com/",
        "https://pirateship.com/login",
        "https://pirateship.com/",
        "https://app.pirateship.com/login",
        "https://app.pirateship.com/"
    ]
    
    options = webdriver.ChromeOptions()
    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    options.add_argument("--disable-blink-features=AutomationControlled")
    
    async with webdriver.Chrome(options=options) as driver:
        for url in urls_to_check:
            try:
                logger.info(f"🌐 Checking URL: {url}")
                await driver.get(url)
                await driver.sleep(3)
                
                # Get page info
                page_info = await driver.execute_script("""
                return {
                    title: document.title,
                    url: window.location.href,
                    hasEmailInput: !!document.querySelector('input[type="email"]'),
                    hasPasswordInput: !!document.querySelector('input[type="password"]'),
                    hasLoginButton: !!document.querySelector('button[type="submit"]'),
                    hasTurnstile: !!document.querySelector('#cf-turnstile'),
                    bodyText: document.body.innerText.substring(0, 200)
                };
                """)
                
                logger.info(f"📄 Page Title: {page_info.get('title')}")
                logger.info(f"🔗 Final URL: {page_info.get('url')}")
                logger.info(f"📧 Has Email Input: {page_info.get('hasEmailInput')}")
                logger.info(f"🔒 Has Password Input: {page_info.get('hasPasswordInput')}")
                logger.info(f"🔘 Has Login Button: {page_info.get('hasLoginButton')}")
                logger.info(f"🛡️ Has Turnstile: {page_info.get('hasTurnstile')}")
                logger.info(f"📝 Body Text Preview: {page_info.get('bodyText')[:100]}...")
                
                # Check if this looks like a login page
                is_login_page = (
                    page_info.get('hasEmailInput') and 
                    page_info.get('hasPasswordInput') and 
                    page_info.get('hasLoginButton')
                )
                
                if is_login_page:
                    logger.info("✅ This appears to be a valid login page!")
                    
                    # Try to fill in test credentials to see if Turnstile appears
                    try:
                        email_field = await driver.find_element(By.CSS_SELECTOR, "input[type='email']", timeout=5)
                        password_field = await driver.find_element(By.CSS_SELECTOR, "input[type='password']", timeout=5)
                        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
                        
                        logger.info("📝 Filling test credentials...")
                        await email_field.send_keys("<EMAIL>")
                        await password_field.send_keys("testpassword")
                        
                        # Check login button state before clicking
                        is_disabled_before = await login_button.get_dom_attribute('disabled')
                        logger.info(f"🔘 Login button disabled before: {is_disabled_before is not None}")
                        
                        # Click login to see if Turnstile appears
                        await login_button.click()
                        await driver.sleep(3)
                        
                        # Check for Turnstile after clicking
                        turnstile_info = await driver.execute_script("""
                        return {
                            hasTurnstile: !!document.querySelector('#cf-turnstile'),
                            websiteKey: document.querySelector('#cf-turnstile')?.getAttribute('data-sitekey'),
                            hasResponseInput: !!document.querySelector('input[name="cf-turnstile-response"]'),
                            loginButtonDisabled: !!document.querySelector('button[type="submit"]')?.disabled,
                            errorMessages: Array.from(document.querySelectorAll('.error, .alert-danger, [role="alert"]')).map(el => el.innerText)
                        };
                        """)
                        
                        logger.info(f"🛡️ Turnstile after login click: {turnstile_info}")
                        
                        if turnstile_info.get('hasTurnstile'):
                            logger.info("🎯 FOUND TURNSTILE! This is the correct login page.")
                            logger.info(f"🔑 Website Key: {turnstile_info.get('websiteKey')}")
                            return url, turnstile_info
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Could not test login form: {e}")
                
                logger.info("-" * 50)
                
            except Exception as e:
                logger.error(f"❌ Error checking {url}: {e}")
                logger.info("-" * 50)
                continue
        
        logger.warning("⚠️ No valid login page with Turnstile found")
        return None, None

if __name__ == "__main__":
    async def main():
        logger.info("🔍 Checking PirateShip URLs to find correct login page")
        logger.info("=" * 60)
        
        url, turnstile_info = await check_pirateship_urls()
        
        if url:
            logger.info(f"\n✅ FOUND CORRECT LOGIN URL: {url}")
            if turnstile_info:
                logger.info(f"🔑 Turnstile Site Key: {turnstile_info.get('websiteKey')}")
        else:
            logger.info("\n❌ Could not find a working PirateShip login page")
            logger.info("The site may be down, changed URLs, or requires different access")
    
    asyncio.run(main())
