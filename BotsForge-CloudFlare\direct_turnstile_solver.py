"""
Direct Cloudflare Turnstile Solver Integration
Extracts core solving logic from BotsForge-CloudFlare library for direct use
without API layer dependencies.
"""

import asyncio
from time import time
import os
import random
import uuid
from typing import Optional, Dict, Any
import cv2
import numpy as np
import pyautogui
from loguru import logger
from patchright.async_api import async_playwright
from dotenv import load_dotenv

# Import models from the BotsForge library
try:
    from models import CaptchaTask
except ImportError:
    # Create a minimal CaptchaTask class if models not available
    from typing import Optional
    from pydantic import BaseModel

    class CaptchaTask(BaseModel):
        id: Optional[str] = None
        type: str = 'AntiTurnstileTaskProxyLess'
        websiteURL: str
        websiteKey: str

load_dotenv()


class DirectTurnstileSolver:
    """
    Direct integration of BotsForge Turnstile solving logic.
    Bypasses the API layer and provides direct access to solving functions.
    """
    
    def __init__(self, headless: bool = False, proxy: Optional[str] = None):
        self.headless = headless
        self.proxy = self._parse_proxy(proxy) if proxy else None
        self.playwright = None
        self.browser = None
        self.page = None
        
    def _parse_proxy(self, proxy_str: str) -> Optional[Dict[str, Any]]:
        """Parse proxy string into Playwright format"""
        try:
            if '@' in proxy_str:
                auth, server = proxy_str.split('@')
                username, password = auth.split(':')
                host, port = server.split(':')
                return {
                    'server': f'http://{host}:{port}',
                    'username': username,
                    'password': password
                }
            else:
                host, port = proxy_str.split(':')
                return {'server': f'http://{host}:{port}'}
        except Exception as e:
            logger.warning(f"Failed to parse proxy: {e}")
            return None
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
    
    async def initialize(self):
        """Initialize browser and page"""
        try:
            self.playwright = await async_playwright().start()
            
            # Browser launch options
            launch_options = {
                'headless': self.headless,
                'args': [
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--no-sandbox',
                    '--disable-dev-shm-usage'
                ]
            }
            
            if self.proxy:
                launch_options['proxy'] = self.proxy
                
            self.browser = await self.playwright.chromium.launch(**launch_options)
            
            # Create context and page
            context = await self.browser.new_context(
                viewport={"width": 1280, "height": 720},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            
            self.page = await context.new_page()
            
            # Add anti-detection scripts
            await self._add_stealth_scripts()
            
            logger.info("✅ DirectTurnstileSolver initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize DirectTurnstileSolver: {e}")
            await self.cleanup()
            raise
    
    async def _add_stealth_scripts(self):
        """Add stealth scripts to avoid detection"""
        stealth_script = """
        // Override webdriver property
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // Override plugins
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // Override languages
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
        });
        
        // Override permissions
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        """
        
        await self.page.add_init_script(stealth_script)
    
    async def solve_turnstile(self, website_url: str, website_key: str, action: str = '') -> Optional[str]:
        """
        Main function to solve Cloudflare Turnstile
        
        Args:
            website_url: The URL where the Turnstile is located
            website_key: The site key for the Turnstile
            action: Optional action parameter
            
        Returns:
            The solved token or None if failed
        """
        if not self.page:
            raise RuntimeError("Solver not initialized. Use async context manager or call initialize() first.")
        
        try:
            logger.info(f"🎯 Starting Turnstile solving for {website_url}")
            
            # Create task object
            task = CaptchaTask(
                id=str(uuid.uuid4()),
                type='AntiTurnstileTaskProxyLess',
                websiteURL=website_url,
                websiteKey=website_key
            )
            
            # Navigate to the website
            logger.info(f"🌐 Navigating to {website_url}")
            await self.page.goto(website_url, wait_until='domcontentloaded', timeout=30000)
            
            # Load and solve the Turnstile
            await self._load_turnstile_widget(website_key, action)
            token = await self._wait_for_turnstile_token()
            
            if token:
                logger.info(f"✅ Successfully solved Turnstile: {token[:50]}...")
                return token
            else:
                logger.warning("❌ Failed to solve Turnstile")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error solving Turnstile: {e}")
            return None

    async def _load_turnstile_widget(self, website_key: str, action: str = ''):
        """Load Cloudflare Turnstile widget on the page"""
        script = f"""
        // 🧹 Remove any existing captcha overlay
        const existing = document.querySelector('#captcha-overlay');
        if (existing) existing.remove();

        // 🔳 Create overlay container
        const overlay = document.createElement('div');
        overlay.id = 'captcha-overlay';
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100vw';
        overlay.style.height = '100vh';
        overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        overlay.style.display = 'flex';
        overlay.style.justifyContent = 'center';
        overlay.style.alignItems = 'center';
        overlay.style.zIndex = '10000';

        // 🧩 Create Turnstile widget
        const captchaDiv = document.createElement('div');
        captchaDiv.className = 'cf-turnstile';
        captchaDiv.setAttribute('data-sitekey', '{website_key}');
        captchaDiv.setAttribute('data-callback', 'onCaptchaSuccess');
        captchaDiv.setAttribute('data-action', '{action}');
        captchaDiv.setAttribute('data-theme', 'light');
        captchaDiv.setAttribute('data-size', 'normal');

        overlay.appendChild(captchaDiv);
        document.body.appendChild(overlay);

        // 📜 Load Cloudflare Turnstile script
        if (!document.querySelector('script[src*="turnstile"]')) {{
            const script = document.createElement('script');
            script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
        }}

        // Define success callback
        window.onCaptchaSuccess = function(token) {{
            console.log('Turnstile solved:', token);
        }};
        """

        await self.page.evaluate(script)
        logger.info("🎯 Turnstile widget loaded")

        # Wait for widget to initialize
        await asyncio.sleep(2)

    async def _wait_for_turnstile_token(self, timeout: int = 30) -> Optional[str]:
        """Wait for Turnstile token to be generated"""
        logger.info("⏳ Waiting for Turnstile token...")

        start_time = time()
        token = ""

        while not token and (time() - start_time) < timeout:
            await asyncio.sleep(0.5)

            try:
                # Check for token using JavaScript evaluation (more reliable)
                token_data = await self.page.evaluate("""
                () => {
                    const responseInput = document.querySelector('input[name="cf-turnstile-response"]');
                    return {
                        token: responseInput ? responseInput.value : null,
                        inputExists: !!responseInput,
                        turnstileLoaded: !!window.turnstile,
                        iframeCount: document.querySelectorAll('#cf-turnstile iframe').length
                    };
                }
                """)

                if token_data.get('token'):
                    token = token_data['token']
                    logger.info(f"✅ Token found: {token[:50]}...")
                    break

                # Check for checkbox and click if needed
                if await self._check_and_click_checkbox():
                    logger.info("🖱️ Clicked Turnstile checkbox")

            except Exception as e:
                logger.debug(f"Token check error: {e}")
                continue

        if not token:
            logger.warning(f"⏰ Timeout waiting for token after {timeout}s")
            return None

        return token

    async def _check_and_click_checkbox(self) -> bool:
        """Check for Turnstile checkbox and click it using visual recognition"""
        try:
            # Take screenshot
            screenshot_bytes = await self.page.screenshot(full_page=True)

            # Convert to OpenCV format
            screen_np = np.frombuffer(screenshot_bytes, dtype=np.uint8)
            screen = cv2.imdecode(screen_np, cv2.IMREAD_COLOR)

            # Load checkbox template
            template_path = "screens/checkbox.png"
            if not os.path.exists(template_path):
                logger.debug("📸 Checkbox template not found, skipping visual detection")
                return False

            template = cv2.imread(template_path)
            if template is None:
                logger.debug("📸 Failed to load checkbox template")
                return False

            # Perform template matching
            result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)

            # If match confidence is high enough
            if max_val > 0.8:
                logger.info(f"🎯 Checkbox detected with confidence: {max_val:.2f}")

                # Calculate click coordinates
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2

                # Add some randomness to click position
                click_x = center_x + random.randint(-5, 5)
                click_y = center_y + random.randint(-5, 5)

                # Click using PyAutoGUI (more reliable than Playwright for this)
                pyautogui.click(click_x, click_y)

                await asyncio.sleep(1)  # Wait for click to register
                return True

        except Exception as e:
            logger.debug(f"Checkbox detection error: {e}")

        return False

    async def cleanup(self):
        """Clean up browser resources"""
        try:
            if self.page:
                await self.page.close()
                self.page = None

            if self.browser:
                await self.browser.close()
                self.browser = None

            if self.playwright:
                await self.playwright.stop()
                self.playwright = None

            logger.info("🧹 DirectTurnstileSolver cleaned up")

        except Exception as e:
            logger.warning(f"⚠️ Cleanup error: {e}")

    async def solve_on_existing_page(self, page, website_key: str, action: str = '') -> Optional[str]:
        """
        Solve Turnstile on an existing Playwright page
        Useful for integration with existing automation workflows
        """
        try:
            logger.info(f"🎯 Solving Turnstile on existing page with key: {website_key}")

            # Store original page reference
            original_page = self.page
            self.page = page

            # Load and solve Turnstile
            await self._load_turnstile_widget(website_key, action)
            token = await self._wait_for_turnstile_token()

            # Restore original page reference
            self.page = original_page

            return token

        except Exception as e:
            logger.error(f"❌ Error solving on existing page: {e}")
            # Restore original page reference
            self.page = original_page
            return None
