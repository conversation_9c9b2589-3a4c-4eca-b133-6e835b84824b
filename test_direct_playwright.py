#!/usr/bin/env python3
"""
Direct Playwright-based Turnstile solving for PirateShip login
Uses the browser.py logic directly without going through BotsForge API
"""

import asyncio
import sys
import os
from pathlib import Path

# Add BotsForge directory to path
botsforge_path = Path(__file__).parent / "BotsForge-CloudFlare"
sys.path.insert(0, str(botsforge_path))

from patchright.async_api import async_playwright
from loguru import logger
import cv2
import numpy as np
import pyautogui
import random
from time import time

# Test credentials
EMAIL = "<EMAIL>"
PASSWORD = "sacramento209%%"
PIRATESHIP_URL = "https://ship.pirateship.com/"
TURNSTILE_SITE_KEY = "0x4AAAAAAAyYdva70TquiHgp"

class DirectTurnstileSolver:
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.page = None

    async def launch_browser(self):
        """Launch Playwright browser with anti-detection settings"""
        print("🚀 Launching Playwright browser...")
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=False,
            channel='chrome',
            args=[
                '--disable-blink-features=AutomationControlled',
                "--window-size=1200,800",
                "--window-position=100,100"
            ]
        )
        
        context = await self.browser.new_context(
            viewport={"width": 1200, "height": 800},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        
        self.page = await context.new_page()
        await self.inject_anti_shadow_script()
        print("✅ Browser launched successfully")

    async def inject_anti_shadow_script(self):
        """Inject script to handle shadow DOM"""
        await self.page.add_init_script("""
        (function() {
            const originalAttachShadow = Element.prototype.attachShadow;
            Element.prototype.attachShadow = function(init) {
                const shadow = originalAttachShadow.call(this, init);
                if (init.mode === 'closed') {
                    window.__lastClosedShadowRoot = shadow;
                }
                return shadow;
            };
        })();
        """)

    async def navigate_to_pirateship(self):
        """Navigate to PirateShip and enter credentials"""
        print("🌐 Navigating to PirateShip...")
        await self.page.goto(PIRATESHIP_URL)

        # Wait for page to load completely
        await asyncio.sleep(8)  # Increased wait time for SPA to load

        print("📧 Entering credentials...")
        try:
            # Try multiple selectors for email/password fields
            email_selectors = [
                "input[name='email']",
                "input[type='email']",
                "input[placeholder*='email' i]",
                "#email",
                ".email-input"
            ]

            password_selectors = [
                "input[name='password']",
                "input[type='password']",
                "#password",
                ".password-input"
            ]

            # Find email field
            email_input = None
            for selector in email_selectors:
                try:
                    email_input = await self.page.wait_for_selector(selector, timeout=3000)
                    if email_input:
                        print(f"✅ Found email field with selector: {selector}")
                        break
                except:
                    continue

            # Find password field
            password_input = None
            for selector in password_selectors:
                try:
                    password_input = await self.page.wait_for_selector(selector, timeout=3000)
                    if password_input:
                        print(f"✅ Found password field with selector: {selector}")
                        break
                except:
                    continue

            if email_input and password_input:
                await email_input.fill(EMAIL)
                await password_input.fill(PASSWORD)
                print("✅ Credentials entered successfully")
            else:
                print("⚠️ Could not find email/password fields - proceeding anyway")

        except Exception as e:
            print(f"⚠️ Error entering credentials: {e}")

    async def solve_turnstile_directly(self):
        """Solve Turnstile using direct Playwright approach"""
        print("🎯 Starting direct Turnstile solving...")
        
        # Create Turnstile widget dynamically (like browser.py does)
        await self.create_turnstile_widget()
        
        # Wait for token with checkbox detection
        token = await self.wait_for_turnstile_token()
        
        if token:
            print(f"🎉 Turnstile solved! Token: {token[:20]}...")
            return token
        else:
            print("❌ Failed to solve Turnstile")
            return None

    async def create_turnstile_widget(self):
        """Create Turnstile widget dynamically (adapted from browser.py)"""
        print("🔧 Creating Turnstile widget...")

        script = f"""
        // Remove any existing captcha overlay
        const existing = document.querySelector('#captcha-overlay');
        if (existing) existing.remove();

        // Create overlay for Turnstile
        const overlay = document.createElement('div');
        overlay.id = 'captcha-overlay';
        overlay.style.position = 'fixed';
        overlay.style.top = '50px';
        overlay.style.right = '50px';
        overlay.style.width = '350px';
        overlay.style.height = '250px';
        overlay.style.backgroundColor = 'rgba(255, 255, 255, 0.98)';
        overlay.style.border = '2px solid #0066cc';
        overlay.style.borderRadius = '12px';
        overlay.style.display = 'flex';
        overlay.style.flexDirection = 'column';
        overlay.style.justifyContent = 'center';
        overlay.style.alignItems = 'center';
        overlay.style.zIndex = '999999';
        overlay.style.boxShadow = '0 8px 16px rgba(0,0,0,0.3)';
        overlay.style.fontFamily = 'Arial, sans-serif';

        // Add title
        const title = document.createElement('div');
        title.textContent = 'Solving Turnstile...';
        title.style.marginBottom = '15px';
        title.style.fontSize = '16px';
        title.style.fontWeight = 'bold';
        title.style.color = '#333';
        overlay.appendChild(title);

        // Add Turnstile div
        const captchaDiv = document.createElement('div');
        captchaDiv.className = 'cf-turnstile';
        captchaDiv.id = 'turnstile-widget';
        captchaDiv.setAttribute('data-sitekey', '{TURNSTILE_SITE_KEY}');
        captchaDiv.setAttribute('data-callback', 'onCaptchaSuccess');
        captchaDiv.setAttribute('data-action', '');
        captchaDiv.setAttribute('data-theme', 'light');

        overlay.appendChild(captchaDiv);
        document.body.appendChild(overlay);

        // Add success callback
        window.onCaptchaSuccess = function(token) {{
            console.log('✅ Turnstile success:', token);
            window.turnstileToken = token;
            window.cfTurnstileResponse = token;

            // Update title
            const titleEl = document.querySelector('#captcha-overlay div');
            if (titleEl) titleEl.textContent = '✅ Turnstile Solved!';
        }};

        // Load Cloudflare Turnstile script if not already loaded
        if (!document.querySelector('script[src*="turnstile"]')) {{
            console.log('📜 Loading Turnstile script...');
            const script = document.createElement('script');
            script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
            script.async = true;
            script.defer = true;
            script.onload = function() {{
                console.log('✅ Turnstile script loaded');
                // Force render after script loads
                setTimeout(() => {{
                    if (window.turnstile && window.turnstile.render) {{
                        try {{
                            window.turnstile.render('#turnstile-widget', {{
                                sitekey: '{TURNSTILE_SITE_KEY}',
                                callback: window.onCaptchaSuccess,
                                theme: 'light'
                            }});
                            console.log('🔄 Turnstile manually rendered');
                        }} catch (e) {{
                            console.log('⚠️ Manual render failed:', e);
                        }}
                    }}
                }}, 1000);
            }};
            document.head.appendChild(script);
        }} else {{
            console.log('📜 Turnstile script already loaded');
            // Try to render immediately if script is already loaded
            if (window.turnstile && window.turnstile.render) {{
                try {{
                    window.turnstile.render('#turnstile-widget', {{
                        sitekey: '{TURNSTILE_SITE_KEY}',
                        callback: window.onCaptchaSuccess,
                        theme: 'light'
                    }});
                    console.log('🔄 Turnstile rendered immediately');
                }} catch (e) {{
                    console.log('⚠️ Immediate render failed:', e);
                }}
            }}
        }}

        """

        await self.page.evaluate(script)
        print("✅ Turnstile widget created")

        # Wait a moment for the widget to initialize
        await asyncio.sleep(3)

    async def wait_for_turnstile_token(self, timeout=30):
        """Wait for Turnstile token with checkbox detection (adapted from browser.py)"""
        print("⏳ Waiting for Turnstile token...")

        start_time = time()
        token = ""

        while not token and (time() - start_time) < timeout:
            await asyncio.sleep(0.5)

            try:
                # Check for token in input field using locator
                token_locator = self.page.locator('input[name="cf-turnstile-response"]')
                if await token_locator.count() > 0:
                    token = await token_locator.first.input_value()
                    if token:
                        print(f'✅ Got captcha token: {token[:20]}...')
                        break

                # Also check global variables
                token_from_js = await self.page.evaluate("""
                () => {
                    return window.turnstileToken ||
                           window.cfTurnstileResponse ||
                           document.querySelector('input[name="cf-turnstile-response"]')?.value ||
                           null;
                }
                """)

                if token_from_js:
                    token = token_from_js
                    print(f'✅ Got token from JavaScript: {token[:20]}...')
                    break

                # Check for checkbox and click if found
                if await self.check_for_checkbox():
                    print("🖱️ Clicked Turnstile checkbox")
                    await asyncio.sleep(3)  # Wait for processing

            except Exception as e:
                # Only log every 10 seconds to reduce spam
                if int(time() - start_time) % 10 == 0:
                    print(f"⏳ Still waiting for token... ({int(time() - start_time)}s)")

        if not token:
            print(f"⏰ Timeout waiting for Turnstile token ({timeout}s)")
            return None

        return token

    async def check_for_checkbox(self):
        """Check for Turnstile checkbox using template matching (from browser.py)"""
        try:
            # Take screenshot
            image_bytes = await self.page.screenshot(full_page=True)
            
            # Convert for OpenCV
            screen_np = np.frombuffer(image_bytes, dtype=np.uint8)
            screen = cv2.imdecode(screen_np, cv2.IMREAD_COLOR)
            
            # Load checkbox template
            checkbox_template_path = "BotsForge-CloudFlare/screens/checkbox.png"
            if not os.path.exists(checkbox_template_path):
                # Create a simple checkbox template if it doesn't exist
                print("⚠️ Checkbox template not found, using fallback detection")
                return await self.fallback_checkbox_detection()
            
            template = cv2.imread(checkbox_template_path)
            
            # Template matching
            result = cv2.matchTemplate(screen, template, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)
            
            if max_val > 0.8:  # Lower threshold than original 0.9
                print(f"✅ Checkbox detected! Confidence: {max_val}")
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                
                # Click using Playwright
                await self.page.mouse.click(center_x, center_y)
                return True
                
        except Exception as e:
            print(f"⚠️ Error in checkbox detection: {e}")
            
        return False

    async def fallback_checkbox_detection(self):
        """Fallback checkbox detection using Playwright selectors"""
        try:
            # Look for common Turnstile checkbox selectors
            selectors = [
                'input[type="checkbox"]',
                '.cf-turnstile input',
                '[data-testid="checkbox"]',
                '.challenge-form input[type="checkbox"]'
            ]
            
            for selector in selectors:
                checkbox = await self.page.query_selector(selector)
                if checkbox:
                    await checkbox.click()
                    print(f"✅ Clicked checkbox using selector: {selector}")
                    return True
                    
        except Exception as e:
            print(f"⚠️ Fallback checkbox detection failed: {e}")
            
        return False

    async def inject_token_and_submit(self, token):
        """Inject token into PirateShip form and submit"""
        print("💉 Injecting token into PirateShip form...")
        
        injection_result = await self.page.evaluate(f"""
        // Create cf-turnstile-response input if it doesn't exist
        let captchaInput = document.querySelector('[name="cf-turnstile-response"]');
        if (!captchaInput) {{
            captchaInput = document.createElement('input');
            captchaInput.type = 'hidden';
            captchaInput.name = 'cf-turnstile-response';
            const form = document.querySelector('form') || document.body;
            form.appendChild(captchaInput);
        }}
        
        // Set token value
        captchaInput.value = '{token}';
        captchaInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
        captchaInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
        
        // Enable submit button
        const submitButton = document.querySelector('button[type="submit"]');
        if (submitButton) {{
            submitButton.disabled = false;
            submitButton.removeAttribute('disabled');
        }}
        
        // Set global variables
        window.turnstileToken = '{token}';
        window.cfTurnstileResponse = '{token}';
        
        return {{
            inputCreated: !!captchaInput,
            buttonEnabled: submitButton ? !submitButton.disabled : false
        }};
        """)
        
        print(f"💉 Token injection result: {injection_result}")
        
        # Click submit button
        try:
            submit_button = await self.page.query_selector('button[type="submit"]')
            if submit_button:
                await submit_button.click()
                print("🚀 Clicked submit button")
                
                # Wait for navigation or response
                await asyncio.sleep(5)
                
                current_url = self.page.url
                print(f"📍 Current URL: {current_url}")
                
                if "login" not in current_url.lower():
                    print("✅ LOGIN SUCCESSFUL!")
                    return True
                else:
                    print("❌ Still on login page")
                    return False
            else:
                print("❌ Submit button not found")
                return False
                
        except Exception as e:
            print(f"❌ Error during submit: {e}")
            return False

    async def cleanup(self):
        """Clean up browser resources"""
        try:
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
        except Exception as e:
            print(f"⚠️ Error during cleanup: {e}")

async def main():
    """Main test function"""
    print("🔥 Direct Playwright Turnstile Solver Test")
    print("=" * 50)
    
    solver = DirectTurnstileSolver()
    
    try:
        # Launch browser and navigate
        await solver.launch_browser()
        await solver.navigate_to_pirateship()
        
        # Solve Turnstile directly
        token = await solver.solve_turnstile_directly()
        
        if token:
            # Inject token and submit
            success = await solver.inject_token_and_submit(token)
            if success:
                print("🎉 COMPLETE SUCCESS!")
            else:
                print("⚠️ Token solved but login failed")
        else:
            print("❌ Failed to solve Turnstile")
            
    except Exception as e:
        print(f"❌ Error in main process: {e}")
    finally:
        await solver.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
