"""
Test Script for BotsForge Direct Integration
Verifies that the direct integration is working correctly.
"""

import asyncio
import sys
import os
from loguru import logger

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

try:
    from turnstile_integration import (
        TurnstileIntegration, 
        solve_turnstile_quick, 
        enhanced_turnstile_solver
    )
    logger.info("✅ Successfully imported integration modules")
except ImportError as e:
    logger.error(f"❌ Failed to import integration modules: {e}")
    sys.exit(1)


async def test_basic_import():
    """Test that all required modules can be imported"""
    try:
        # Test BotsForge imports
        sys.path.append('BotsForge-CloudFlare')
        from models import CaptchaTask
        from direct_turnstile_solver import DirectTurnstileSolver
        
        logger.info("✅ BotsForge modules imported successfully")
        return True
    except ImportError as e:
        logger.error(f"❌ Failed to import BotsForge modules: {e}")
        return False


async def test_solver_initialization():
    """Test that the solver can be initialized"""
    try:
        async with TurnstileIntegration(headless=True) as integration:
            logger.info("✅ TurnstileIntegration initialized successfully")
            return True
    except Exception as e:
        logger.error(f"❌ Failed to initialize TurnstileIntegration: {e}")
        return False


async def test_turnstile_detection():
    """Test Turnstile detection on a known site"""
    try:
        # Use a known test site with Turnstile
        test_url = "https://faucet.sonic.game"
        test_key = "0x4AAAAAAAc6HG1RMG_8EHSC"
        
        logger.info(f"🎯 Testing Turnstile detection on {test_url}")
        
        async with TurnstileIntegration(headless=True) as integration:
            # Initialize solver
            if integration.solver:
                logger.info("✅ Solver initialized for detection test")
                
                # Test navigation (without full solving to avoid rate limits)
                await integration.solver.page.goto(test_url, timeout=30000)
                logger.info("✅ Successfully navigated to test site")
                
                # Check if Turnstile elements can be detected
                turnstile_present = await integration.solver.page.evaluate("""
                () => {
                    return {
                        hasTurnstileScript: !!document.querySelector('script[src*="turnstile"]'),
                        hasTurnstileDiv: !!document.querySelector('.cf-turnstile, #cf-turnstile, [data-sitekey]'),
                        pageLoaded: document.readyState === 'complete'
                    };
                }
                """)
                
                logger.info(f"🔍 Turnstile detection results: {turnstile_present}")
                return True
                
    except Exception as e:
        logger.error(f"❌ Turnstile detection test failed: {e}")
        return False


async def test_checkbox_template():
    """Test if checkbox template is available"""
    template_path = "screens/checkbox.png"
    botsforge_template = "BotsForge-CloudFlare/screens/checkbox.png"
    
    if os.path.exists(template_path):
        logger.info("✅ Checkbox template found at screens/checkbox.png")
        return True
    elif os.path.exists(botsforge_template):
        logger.info("✅ Checkbox template found in BotsForge directory")
        logger.info("💡 Consider copying to screens/checkbox.png for better performance")
        return True
    else:
        logger.warning("⚠️ Checkbox template not found - visual detection may not work")
        logger.info("💡 Copy BotsForge-CloudFlare/screens/checkbox.png to screens/checkbox.png")
        return False


async def test_dependencies():
    """Test that all required dependencies are available"""
    dependencies = [
        ('patchright', 'Patchright (Playwright)'),
        ('cv2', 'OpenCV'),
        ('numpy', 'NumPy'),
        ('pyautogui', 'PyAutoGUI'),
        ('loguru', 'Loguru'),
        ('pydantic', 'Pydantic')
    ]
    
    all_good = True
    
    for module, name in dependencies:
        try:
            __import__(module)
            logger.info(f"✅ {name} available")
        except ImportError:
            logger.error(f"❌ {name} not available - install with: pip install {module}")
            all_good = False
    
    return all_good


async def test_browser_installation():
    """Test if Playwright browsers are installed"""
    try:
        from patchright.async_api import async_playwright
        
        async with async_playwright() as p:
            try:
                browser = await p.chromium.launch(headless=True)
                await browser.close()
                logger.info("✅ Chromium browser available")
                return True
            except Exception as e:
                logger.error(f"❌ Chromium browser not available: {e}")
                logger.info("💡 Install with: patchright install chromium")
                return False
                
    except Exception as e:
        logger.error(f"❌ Playwright not available: {e}")
        return False


async def run_all_tests():
    """Run all tests and provide a summary"""
    logger.info("🧪 Starting BotsForge Direct Integration Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Basic Import Test", test_basic_import),
        ("Dependencies Test", test_dependencies),
        ("Browser Installation Test", test_browser_installation),
        ("Checkbox Template Test", test_checkbox_template),
        ("Solver Initialization Test", test_solver_initialization),
        ("Turnstile Detection Test", test_turnstile_detection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🔬 Running: {test_name}")
        logger.info("-" * 40)
        
        try:
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.warning(f"⚠️ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\n🏁 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Integration is ready to use.")
        return True
    else:
        logger.warning("⚠️ Some tests failed. Check the logs above for details.")
        return False


async def quick_integration_test():
    """Quick test of the actual integration functionality"""
    logger.info("\n🚀 Running Quick Integration Test")
    logger.info("-" * 40)
    
    try:
        # Test the quick solve function with a timeout
        logger.info("Testing quick solve function...")
        
        # Note: This is a minimal test that doesn't actually solve to avoid rate limits
        # In a real scenario, you would use actual website parameters
        
        async with TurnstileIntegration(headless=True) as integration:
            if integration.solver:
                logger.info("✅ Integration ready for use")
                
                # Test page navigation
                await integration.solver.page.goto("https://example.com", timeout=10000)
                logger.info("✅ Page navigation works")
                
                return True
            else:
                logger.error("❌ Integration solver not initialized")
                return False
                
    except Exception as e:
        logger.error(f"❌ Quick integration test failed: {e}")
        return False


if __name__ == "__main__":
    async def main():
        """Main test runner"""
        logger.info("🎯 BotsForge Direct Integration Test Suite")
        logger.info("This will verify that the integration is properly set up")
        
        # Run all tests
        all_passed = await run_all_tests()
        
        if all_passed:
            # Run quick integration test
            integration_works = await quick_integration_test()
            
            if integration_works:
                logger.info("\n🎉 SUCCESS: Integration is fully functional!")
                logger.info("You can now use the direct BotsForge integration in your automation.")
            else:
                logger.warning("\n⚠️ Basic tests passed but integration test failed.")
        else:
            logger.error("\n❌ Setup incomplete. Please fix the failed tests above.")
        
        logger.info("\n📚 Next steps:")
        logger.info("1. Check INTEGRATION_GUIDE.md for usage examples")
        logger.info("2. Run example_integration.py for practical examples")
        logger.info("3. Integrate with your existing automation workflow")
    
    # Run the test suite
    asyncio.run(main())
