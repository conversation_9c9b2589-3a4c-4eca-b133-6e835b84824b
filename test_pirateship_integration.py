"""
Test script for PirateShip Turnstile integration
Tests the direct BotsForge integration with PirateShip login
"""

import asyncio
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
from turnstile_integration import enhanced_turnstile_solver, solve_turnstile_quick
from loguru import logger

# Test credentials
email = "<EMAIL>"
password = "sacramento209%%"
element_timeout = 15


async def test_pirateship_turnstile_integration():
    """
    Test the direct BotsForge Turnstile integration with PirateShip login
    """
    logger.info("🚀 Starting PirateShip Turnstile integration test")
    
    options = webdriver.ChromeOptions()
    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    options.add_argument("--disable-blink-features=AutomationControlled")
    
    async with webdriver.Chrome(options=options) as driver:
        try:
            logger.info("🌐 Navigating to PirateShip login page")
            await driver.get("https://ship.pirateship.com/login")
            await driver.sleep(3)
            
            # Fill in credentials
            logger.info("📝 Filling login credentials")
            email_field = await driver.find_element(By.CSS_SELECTOR, "input[type='email']", timeout=element_timeout)
            await email_field.send_keys(email)
            
            password_field = await driver.find_element(By.CSS_SELECTOR, "input[type='password']", timeout=element_timeout)
            await password_field.send_keys(password)
            
            # Check initial login button state
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
            is_disabled_before = await login_button.get_dom_attribute('disabled')
            logger.info(f"🔘 Login button disabled before click: {is_disabled_before is not None}")
            
            # Click login button to trigger Turnstile
            logger.info("🖱️ Clicking login button to trigger Turnstile")
            await login_button.click()
            await driver.sleep(2)
            
            # Check if Turnstile appeared
            try:
                turnstile_element = await driver.find_element(By.CSS_SELECTOR, "#cf-turnstile", timeout=5)
                logger.info("🎯 Turnstile detected on page")
                
                # Extract Turnstile data
                turnstile_data = await driver.execute_script("""
                return {
                    websiteKey: document.querySelector('#cf-turnstile')?.getAttribute('data-sitekey'),
                    websiteURL: window.location.href,
                    action: document.querySelector('#cf-turnstile')?.getAttribute('data-action') || '',
                    turnstileVisible: !!document.querySelector('#cf-turnstile'),
                    iframeCount: document.querySelectorAll('#cf-turnstile iframe').length
                };
                """)
                
                logger.info(f"📊 Turnstile data: {turnstile_data}")
                
                if turnstile_data.get('websiteKey'):
                    logger.info(f"🔑 Turnstile site key: {turnstile_data['websiteKey']}")
                    
                    # Test Method 1: Enhanced solver
                    logger.info("🎯 Testing Method 1: Enhanced Turnstile solver")
                    success = await enhanced_turnstile_solver(
                        driver_or_page=driver,
                        website_url="https://ship.pirateship.com/login"
                    )
                    
                    if success:
                        logger.info("✅ Method 1 successful!")
                        
                        # Check login button state after solving
                        await driver.sleep(2)
                        is_disabled_after = await login_button.get_dom_attribute('disabled')
                        logger.info(f"🔘 Login button disabled after solving: {is_disabled_after is not None}")
                        
                        if is_disabled_after is None:
                            logger.info("🎉 Login button enabled - Turnstile solved successfully!")
                            
                            # Try to proceed with login
                            await login_button.click()
                            await driver.sleep(5)
                            
                            # Check for successful login
                            current_url = await driver.current_url
                            logger.info(f"📍 Current URL after login attempt: {current_url}")
                            
                            if "dashboard" in current_url or "ship" in current_url:
                                logger.info("🎉 Login successful - redirected to dashboard!")
                                return True
                            else:
                                logger.warning("⚠️ Login may not have succeeded")
                        else:
                            logger.warning("⚠️ Login button still disabled after solving")
                    else:
                        logger.warning("⚠️ Method 1 failed, trying Method 2")
                        
                        # Test Method 2: Direct solving
                        logger.info("🎯 Testing Method 2: Direct Turnstile solving")
                        token = await solve_turnstile_quick(
                            website_url=turnstile_data['websiteURL'],
                            website_key=turnstile_data['websiteKey'],
                            action=turnstile_data.get('action', ''),
                            headless=False
                        )
                        
                        if token:
                            logger.info(f"🎉 Token obtained: {token[:50]}...")
                            
                            # Inject token
                            injection_success = await driver.execute_script(f"""
                            const responseInput = document.querySelector('input[name="cf-turnstile-response"]');
                            if (responseInput) {{
                                responseInput.value = '{token}';
                                responseInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                responseInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            }}
                            
                            if (typeof window.tsCallback === 'function') {{
                                window.tsCallback('{token}');
                            }}
                            
                            return responseInput ? true : false;
                            """)
                            
                            if injection_success:
                                logger.info("💉 Token injected successfully")
                                await driver.sleep(2)
                                
                                # Check login button state
                                is_disabled_after = await login_button.get_dom_attribute('disabled')
                                if is_disabled_after is None:
                                    logger.info("✅ Method 2 successful - login button enabled!")
                                    return True
                                else:
                                    logger.warning("⚠️ Login button still disabled after token injection")
                            else:
                                logger.error("❌ Token injection failed")
                        else:
                            logger.error("❌ Failed to obtain token")
                else:
                    logger.warning("⚠️ Could not extract Turnstile site key")
                    
            except Exception as e:
                logger.info("ℹ️ No Turnstile detected or already solved")
                
                # Check if login button is enabled without Turnstile
                is_disabled = await login_button.get_dom_attribute('disabled')
                if is_disabled is None:
                    logger.info("✅ Login button enabled - no Turnstile required")
                    return True
                else:
                    logger.warning("⚠️ Login button disabled but no Turnstile found")
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Test failed with error: {e}")
            return False


async def test_standalone_solving():
    """
    Test standalone Turnstile solving without browser automation
    """
    logger.info("🧪 Testing standalone Turnstile solving")
    
    try:
        # Test with a known Turnstile site
        token = await solve_turnstile_quick(
            website_url="https://faucet.sonic.game",
            website_key="0x4AAAAAAAc6HG1RMG_8EHSC",
            headless=True
        )
        
        if token:
            logger.info(f"✅ Standalone solving successful: {token[:50]}...")
            return True
        else:
            logger.warning("⚠️ Standalone solving failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Standalone test failed: {e}")
        return False


if __name__ == "__main__":
    async def main():
        """Run all tests"""
        logger.info("🎯 Starting PirateShip Turnstile Integration Tests")
        logger.info("=" * 60)
        
        # Test 1: Standalone solving
        logger.info("\n🧪 Test 1: Standalone Turnstile Solving")
        logger.info("-" * 40)
        standalone_result = await test_standalone_solving()
        
        # Test 2: PirateShip integration
        logger.info("\n🧪 Test 2: PirateShip Integration")
        logger.info("-" * 40)
        pirateship_result = await test_pirateship_turnstile_integration()
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("📊 TEST RESULTS SUMMARY")
        logger.info("=" * 60)
        
        logger.info(f"✅ Standalone Solving: {'PASS' if standalone_result else 'FAIL'}")
        logger.info(f"✅ PirateShip Integration: {'PASS' if pirateship_result else 'FAIL'}")
        
        if standalone_result and pirateship_result:
            logger.info("\n🎉 All tests passed! Integration is working correctly.")
            logger.info("You can now use the updated PirateApi.py with Turnstile support.")
        elif standalone_result:
            logger.info("\n⚠️ Standalone solving works, but PirateShip integration needs attention.")
            logger.info("Check the PirateShip-specific implementation.")
        else:
            logger.info("\n❌ Tests failed. Check the setup and try again.")
        
        return standalone_result and pirateship_result
    
    # Run tests
    result = asyncio.run(main())
    print(f"\n🏁 Final Result: {'SUCCESS' if result else 'NEEDS ATTENTION'}")
