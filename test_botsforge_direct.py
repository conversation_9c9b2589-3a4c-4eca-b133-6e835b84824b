"""
Simple test to verify the BotsForge library works directly
"""

import asyncio
import sys
import os
from loguru import logger

# Add BotsForge to path
sys.path.insert(0, 'BotsForge-CloudFlare')

from browser import Browser
from models import CaptchaTask


async def test_botsforge_direct():
    """Test the original BotsForge browser solving directly"""
    logger.info("🧪 Testing BotsForge Browser class directly")
    
    try:
        # Create a task
        task = CaptchaTask(
            id="test-123",
            type='AntiTurnstileTaskProxyLess',
            websiteURL='https://faucet.sonic.game',
            websiteKey='0x4AAAAAAAc6HG1RMG_8EHSC'
        )
        
        logger.info(f"📋 Created task: {task.websiteURL}")
        
        # Create browser instance
        browser = Browser()
        
        # Solve the captcha
        logger.info("🎯 Starting solve_captcha...")
        token = await browser.solve_captcha(task)
        
        if token:
            logger.info(f"✅ Success! Token: {token[:50]}...")
            return True
        else:
            logger.warning("❌ No token returned")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        return False


async def test_simple_integration():
    """Test a simple integration approach"""
    logger.info("🧪 Testing simple integration approach")
    
    try:
        from turnstile_integration import solve_turnstile_quick
        
        # Test with a simpler approach
        token = await solve_turnstile_quick(
            website_url="https://faucet.sonic.game",
            website_key="0x4AAAAAAAc6HG1RMG_8EHSC",
            headless=True
        )
        
        if token:
            logger.info(f"✅ Simple integration success: {token[:50]}...")
            return True
        else:
            logger.warning("❌ Simple integration failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Simple integration error: {e}")
        return False


if __name__ == "__main__":
    async def main():
        logger.info("🎯 Testing BotsForge Direct Integration")
        logger.info("=" * 50)
        
        # Test 1: Direct BotsForge
        logger.info("\n🧪 Test 1: Direct BotsForge Browser")
        logger.info("-" * 30)
        result1 = await test_botsforge_direct()
        
        # Test 2: Simple integration
        logger.info("\n🧪 Test 2: Simple Integration")
        logger.info("-" * 30)
        result2 = await test_simple_integration()
        
        # Results
        logger.info("\n" + "=" * 50)
        logger.info("📊 RESULTS")
        logger.info("=" * 50)
        logger.info(f"Direct BotsForge: {'✅ PASS' if result1 else '❌ FAIL'}")
        logger.info(f"Simple Integration: {'✅ PASS' if result2 else '❌ FAIL'}")
        
        if result1 or result2:
            logger.info("\n🎉 At least one method works!")
        else:
            logger.info("\n❌ Both methods failed - check setup")
    
    asyncio.run(main())
