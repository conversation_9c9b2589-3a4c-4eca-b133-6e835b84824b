"""
Final test for PirateShip Turnstile integration
Tests the complete workflow with the working BotsForge integration
"""

import asyncio
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
from simple_turnstile_integration import enhanced_pirateship_turnstile_solver
from loguru import logger

# Test credentials
email = "<EMAIL>"
password = "sacramento209%%"
element_timeout = 15


async def test_complete_pirateship_workflow():
    """
    Test the complete PirateShip login workflow with Turnstile solving
    """
    logger.info("🚀 Testing complete PirateShip workflow with BotsForge Turnstile integration")
    
    options = webdriver.ChromeOptions()
    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    options.add_argument("--disable-blink-features=AutomationControlled")
    
    async with webdriver.Chrome(options=options) as driver:
        try:
            logger.info("🌐 Navigating to PirateShip login page")
            await driver.get("https://ship.pirateship.com/login")
            await driver.sleep(3)
            
            # Fill in credentials
            logger.info("📝 Filling login credentials")
            email_field = await driver.find_element(By.CSS_SELECTOR, "input[type='email']", timeout=element_timeout)
            await email_field.send_keys(email)
            
            password_field = await driver.find_element(By.CSS_SELECTOR, "input[type='password']", timeout=element_timeout)
            await password_field.send_keys(password)
            
            # Check initial login button state
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
            is_disabled_before = await login_button.get_dom_attribute('disabled')
            logger.info(f"🔘 Login button disabled before click: {is_disabled_before is not None}")
            
            # Click login button to trigger Turnstile
            logger.info("🖱️ Clicking login button to trigger Turnstile")
            await login_button.click()
            await driver.sleep(3)
            
            # Check if Turnstile is present
            turnstile_check = await driver.execute_script("""
            return {
                turnstileExists: !!document.querySelector('#cf-turnstile'),
                websiteKey: document.querySelector('#cf-turnstile')?.getAttribute('data-sitekey'),
                loginButtonDisabled: !!document.querySelector('button[type="submit"]')?.disabled
            };
            """)
            
            logger.info(f"📊 Page state: {turnstile_check}")
            
            if turnstile_check.get('turnstileExists'):
                logger.info("🎯 Turnstile detected - using BotsForge solver")
                
                # Use the enhanced PirateShip Turnstile solver
                success = await enhanced_pirateship_turnstile_solver(driver)
                
                if success:
                    logger.info("✅ Turnstile solved successfully!")
                    
                    # Wait for page to process
                    await driver.sleep(3)
                    
                    # Check login button state after solving
                    is_disabled_after = await login_button.get_dom_attribute('disabled')
                    logger.info(f"🔘 Login button disabled after solving: {is_disabled_after is not None}")
                    
                    if is_disabled_after is None:
                        logger.info("🎉 Login button enabled - attempting login")
                        
                        # Click login button again if needed
                        await login_button.click()
                        await driver.sleep(5)
                        
                        # Check for successful login
                        current_url = await driver.current_url
                        logger.info(f"📍 Current URL after login: {current_url}")
                        
                        if "dashboard" in current_url or "ship" in current_url:
                            logger.info("🎉 LOGIN SUCCESSFUL - Redirected to dashboard!")
                            return True
                        else:
                            # Check for error messages
                            try:
                                error_element = await driver.find_element(By.CSS_SELECTOR, ".error, .alert-danger, [role='alert']", timeout=3)
                                error_text = await error_element.text
                                logger.warning(f"⚠️ Login error: {error_text}")
                            except:
                                logger.info("ℹ️ No error messages found - login may have succeeded")
                                
                            # Check if we're still on login page
                            if "login" in current_url:
                                logger.warning("⚠️ Still on login page - login may have failed")
                                return False
                            else:
                                logger.info("✅ Navigated away from login page - likely successful")
                                return True
                    else:
                        logger.warning("⚠️ Login button still disabled after Turnstile solving")
                        return False
                else:
                    logger.error("❌ Turnstile solving failed")
                    return False
            else:
                logger.info("ℹ️ No Turnstile detected - checking if login is possible")
                
                # Check if login button is enabled without Turnstile
                is_disabled = await login_button.get_dom_attribute('disabled')
                if is_disabled is None:
                    logger.info("✅ Login button enabled without Turnstile - proceeding")
                    
                    await login_button.click()
                    await driver.sleep(5)
                    
                    current_url = await driver.current_url
                    if "dashboard" in current_url or "ship" in current_url:
                        logger.info("🎉 LOGIN SUCCESSFUL without Turnstile!")
                        return True
                    else:
                        logger.warning("⚠️ Login failed even without Turnstile")
                        return False
                else:
                    logger.warning("⚠️ Login button disabled but no Turnstile found")
                    return False
            
        except Exception as e:
            logger.error(f"❌ Test failed with error: {e}")
            return False


async def test_turnstile_detection():
    """
    Test just the Turnstile detection and parameter extraction
    """
    logger.info("🔍 Testing Turnstile detection on PirateShip")
    
    options = webdriver.ChromeOptions()
    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    options.add_argument("--disable-blink-features=AutomationControlled")
    
    async with webdriver.Chrome(options=options) as driver:
        try:
            await driver.get("https://ship.pirateship.com/login")
            await driver.sleep(3)
            
            # Fill credentials to trigger Turnstile
            email_field = await driver.find_element(By.CSS_SELECTOR, "input[type='email']", timeout=element_timeout)
            await email_field.send_keys(email)
            
            password_field = await driver.find_element(By.CSS_SELECTOR, "input[type='password']", timeout=element_timeout)
            await password_field.send_keys(password)
            
            # Click login to trigger Turnstile
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
            await login_button.click()
            await driver.sleep(3)
            
            # Extract all Turnstile-related information
            turnstile_info = await driver.execute_script("""
            return {
                turnstileDiv: !!document.querySelector('#cf-turnstile'),
                dataSitekey: document.querySelector('#cf-turnstile')?.getAttribute('data-sitekey'),
                dataAction: document.querySelector('#cf-turnstile')?.getAttribute('data-action'),
                responseInput: !!document.querySelector('input[name="cf-turnstile-response"]'),
                iframeCount: document.querySelectorAll('#cf-turnstile iframe').length,
                allTurnstileElements: Array.from(document.querySelectorAll('[data-sitekey]')).map(el => ({
                    tagName: el.tagName,
                    sitekey: el.getAttribute('data-sitekey'),
                    action: el.getAttribute('data-action')
                })),
                loginButtonDisabled: !!document.querySelector('button[type="submit"]')?.disabled,
                pageTitle: document.title,
                currentURL: window.location.href
            };
            """)
            
            logger.info("📊 Turnstile Detection Results:")
            logger.info(f"   Turnstile Div: {turnstile_info.get('turnstileDiv')}")
            logger.info(f"   Site Key: {turnstile_info.get('dataSitekey')}")
            logger.info(f"   Action: {turnstile_info.get('dataAction')}")
            logger.info(f"   Response Input: {turnstile_info.get('responseInput')}")
            logger.info(f"   Iframe Count: {turnstile_info.get('iframeCount')}")
            logger.info(f"   All Turnstile Elements: {turnstile_info.get('allTurnstileElements')}")
            logger.info(f"   Login Button Disabled: {turnstile_info.get('loginButtonDisabled')}")
            logger.info(f"   Page Title: {turnstile_info.get('pageTitle')}")
            logger.info(f"   Current URL: {turnstile_info.get('currentURL')}")
            
            return turnstile_info.get('turnstileDiv', False)
            
        except Exception as e:
            logger.error(f"❌ Detection test failed: {e}")
            return False


if __name__ == "__main__":
    async def main():
        """Run all tests"""
        logger.info("🎯 PirateShip Turnstile Integration - Final Test")
        logger.info("=" * 60)
        
        # Test 1: Turnstile detection
        logger.info("\n🔍 Test 1: Turnstile Detection")
        logger.info("-" * 40)
        detection_result = await test_turnstile_detection()
        
        # Test 2: Complete workflow
        logger.info("\n🚀 Test 2: Complete Login Workflow")
        logger.info("-" * 40)
        workflow_result = await test_complete_pirateship_workflow()
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("📊 FINAL TEST RESULTS")
        logger.info("=" * 60)
        
        logger.info(f"Turnstile Detection: {'✅ PASS' if detection_result else '❌ FAIL'}")
        logger.info(f"Complete Workflow: {'✅ PASS' if workflow_result else '❌ FAIL'}")
        
        if workflow_result:
            logger.info("\n🎉 SUCCESS! PirateShip Turnstile integration is working!")
            logger.info("You can now use the updated PirateApi.py with confidence.")
        elif detection_result:
            logger.info("\n⚠️ Turnstile detected but workflow failed.")
            logger.info("The integration can detect Turnstiles but may need refinement.")
        else:
            logger.info("\n❌ Tests failed. Check the setup and try again.")
        
        return workflow_result
    
    # Run tests
    result = asyncio.run(main())
    print(f"\n🏁 Final Result: {'SUCCESS' if result else 'NEEDS ATTENTION'}")
