"""
Example Integration: Direct BotsForge Turnstile Solving
Shows how to integrate the direct solving capabilities into your existing automation workflow.
"""

import asyncio
import sys
import os
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
from loguru import logger

# Import our integration module
from turnstile_integration import TurnstileIntegration, enhanced_turnstile_solver, solve_turnstile_quick

# Test credentials
email = "<EMAIL>"
password = "sacramento209%%"
element_timeout = 15


async def direct_turnstile_login_example():
    """
    Example showing how to use direct BotsForge integration for PirateShip login
    """
    options = webdriver.ChromeOptions()
    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    async with webdriver.Chrome(options=options) as driver:
        try:
            logger.info("🚀 Starting direct BotsForge Turnstile integration example")
            
            # Navigate to PirateShip login
            await driver.get("https://ship.pirateship.com/login")
            await driver.sleep(3)
            
            # Fill in credentials
            logger.info("📝 Filling login credentials")
            email_field = await driver.find_element(By.CSS_SELECTOR, "input[type='email']", timeout=element_timeout)
            await email_field.send_keys(email)
            
            password_field = await driver.find_element(By.CSS_SELECTOR, "input[type='password']", timeout=element_timeout)
            await password_field.send_keys(password)
            
            # Check if Turnstile is present
            logger.info("🔍 Checking for Cloudflare Turnstile...")
            
            try:
                turnstile_element = await driver.find_element(By.CSS_SELECTOR, "#cf-turnstile", timeout=5)
                logger.info("🎯 Turnstile detected - using direct BotsForge solving")
                
                # Method 1: Use the enhanced solver (recommended)
                success = await enhanced_turnstile_solver(
                    driver_or_page=driver,
                    website_url="https://ship.pirateship.com/login"
                )
                
                if success:
                    logger.info("✅ Turnstile solved with enhanced solver")
                else:
                    logger.warning("⚠️ Enhanced solver failed, trying manual approach")
                    
                    # Method 2: Manual approach with extracted parameters
                    turnstile_data = await driver.execute_script("""
                    return {
                        websiteKey: document.querySelector('#cf-turnstile').getAttribute('data-sitekey'),
                        websiteURL: window.location.href,
                        action: document.querySelector('#cf-turnstile').getAttribute('data-action') || ''
                    };
                    """)
                    
                    if turnstile_data and turnstile_data.get('websiteKey'):
                        logger.info(f"🔑 Extracted Turnstile key: {turnstile_data['websiteKey']}")
                        
                        # Solve using standalone method
                        token = await solve_turnstile_quick(
                            website_url=turnstile_data['websiteURL'],
                            website_key=turnstile_data['websiteKey'],
                            action=turnstile_data.get('action', ''),
                            headless=False  # Set to True for production
                        )
                        
                        if token:
                            logger.info(f"🎉 Token obtained: {token[:50]}...")
                            
                            # Inject token into the page
                            injection_success = await driver.execute_script(f"""
                            // Set the cf-turnstile-response input
                            const responseInput = document.querySelector('input[name="cf-turnstile-response"]');
                            if (responseInput) {{
                                responseInput.value = '{token}';
                                responseInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                responseInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            }}
                            
                            // Trigger callback if it exists
                            if (typeof window.tsCallback === 'function') {{
                                window.tsCallback('{token}');
                            }}
                            
                            return responseInput ? true : false;
                            """)
                            
                            if injection_success:
                                logger.info("💉 Token injected successfully")
                                success = True
                            else:
                                logger.error("❌ Token injection failed")
                    
            except Exception as e:
                logger.info("ℹ️ No Turnstile detected or already solved")
                success = True
            
            # Wait a moment for token processing
            await driver.sleep(2)
            
            # Check if login button is enabled
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
            is_disabled = await login_button.get_dom_attribute('disabled')
            
            if is_disabled is None:
                logger.info("✅ Login button is enabled - proceeding with login")
                
                # Click login button
                await login_button.click()
                await driver.sleep(5)
                
                # Check for successful login
                try:
                    # Look for dashboard elements or URL change
                    current_url = await driver.current_url
                    if "dashboard" in current_url or "ship" in current_url:
                        logger.info("🎉 Login successful!")
                        return True
                    else:
                        # Check for error messages
                        try:
                            error_element = await driver.find_element(By.CSS_SELECTOR, ".error, .alert-danger, [role='alert']", timeout=3)
                            error_text = await error_element.text
                            logger.warning(f"⚠️ Login error: {error_text}")
                        except:
                            logger.warning("⚠️ Login status unclear")
                        
                except Exception as e:
                    logger.warning(f"⚠️ Could not verify login status: {e}")
                    
            else:
                logger.error("❌ Login button still disabled after Turnstile solving")
                return False
                
        except Exception as e:
            logger.error(f"❌ Login process failed: {e}")
            return False


async def playwright_integration_example():
    """
    Example showing integration with Playwright (if you prefer Playwright over Selenium)
    """
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        try:
            logger.info("🎭 Starting Playwright integration example")
            
            # Navigate to login page
            await page.goto("https://ship.pirateship.com/login")
            
            # Fill credentials
            await page.fill("input[type='email']", email)
            await page.fill("input[type='password']", password)
            
            # Use the integration to solve Turnstile
            async with TurnstileIntegration(headless=False) as integration:
                # Check for Turnstile
                turnstile_element = await page.query_selector("#cf-turnstile")
                
                if turnstile_element:
                    website_key = await turnstile_element.get_attribute("data-sitekey")
                    
                    if website_key:
                        logger.info(f"🎯 Solving Turnstile with key: {website_key}")
                        
                        # Solve on existing page
                        token = await integration.solve_turnstile_on_page(page, website_key)
                        
                        if token:
                            # Inject token
                            await integration.inject_token_and_continue(page, token)
                            logger.info("✅ Turnstile solved and token injected")
                        else:
                            logger.error("❌ Failed to solve Turnstile")
                            return False
                
                # Wait and click login
                await page.wait_for_timeout(2000)
                await page.click("button[type='submit']")
                
                # Wait for navigation
                await page.wait_for_load_state('networkidle')
                
                if "dashboard" in page.url:
                    logger.info("🎉 Playwright login successful!")
                    return True
                else:
                    logger.warning("⚠️ Login may have failed")
                    return False
                    
        finally:
            await browser.close()


async def batch_solving_example():
    """
    Example showing how to solve multiple Turnstiles in batch
    """
    logger.info("📦 Starting batch Turnstile solving example")
    
    # List of sites with Turnstiles to solve
    sites = [
        {
            "url": "https://faucet.sonic.game",
            "key": "0x4AAAAAAAc6HG1RMG_8EHSC"
        },
        # Add more sites as needed
    ]
    
    results = []
    
    async with TurnstileIntegration(headless=True) as integration:
        for site in sites:
            logger.info(f"🎯 Solving Turnstile for {site['url']}")
            
            token = await integration.solve_turnstile_standalone(
                website_url=site['url'],
                website_key=site['key']
            )
            
            results.append({
                "url": site['url'],
                "key": site['key'],
                "token": token,
                "success": token is not None
            })
            
            if token:
                logger.info(f"✅ Success: {token[:50]}...")
            else:
                logger.warning("❌ Failed")
    
    # Print results
    successful = sum(1 for r in results if r['success'])
    logger.info(f"📊 Batch results: {successful}/{len(results)} successful")
    
    return results


if __name__ == "__main__":
    async def main():
        """Run examples"""
        logger.info("🚀 Starting BotsForge Direct Integration Examples")
        
        # Example 1: Direct integration with existing Selenium workflow
        logger.info("\n" + "="*50)
        logger.info("Example 1: Selenium Integration")
        logger.info("="*50)
        result1 = await direct_turnstile_login_example()
        
        # Example 2: Playwright integration
        logger.info("\n" + "="*50)
        logger.info("Example 2: Playwright Integration")
        logger.info("="*50)
        # Uncomment to test Playwright
        # result2 = await playwright_integration_example()
        
        # Example 3: Batch solving
        logger.info("\n" + "="*50)
        logger.info("Example 3: Batch Solving")
        logger.info("="*50)
        # Uncomment to test batch solving
        # result3 = await batch_solving_example()
        
        logger.info("\n🏁 Examples completed!")
    
    # Run examples
    asyncio.run(main())
