import time
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
import asyncio
import requests
import whisper
import re
import httpx

email = "<EMAIL>"
password = "sacramento209%%"
address = "8391 Folsom Blvd, Sacramento, CA 95826"
send_email = email
phone_number = "9168352323" #Must be valid
recipient_name = "jake"
company_name = "jake corp"
length = "10"
width = "20"
height = "5"
pounds = "5"
ounces = ""
hazardous_materials = True  
element_timeout = 15

options = {
    '1': 'Best',
    '2': 'Cheapest',
    '3': 'USPS',
    '4': 'UPS'
}
selection = options['1']

#Problems marked with !!! ctrl F to them 

model = whisper.load_model("base")


async def clean_text(text):
    # Keep only alphanumeric characters and spaces
    return re.sub(r'[^a-zA-Z0-9\s]', '', text).strip()

async def transcribe(url):
    with open('.temp', 'wb') as f:
        f.write(requests.get(url).content)
        
    result = model.transcribe('.temp')
    cleaned_text = await clean_text(result["text"])
    print("AI: ", result["text"].strip(), " -> ", cleaned_text)
    return cleaned_text

async def solve_with_botsforge_service(website_url, website_key):
    """
    Use BotsForge visual recognition service to solve Turnstile
    """
    import httpx
    import asyncio

    api_key = "pirateship-turnstile-solver-2024"
    base_url = "http://localhost:5033"

    print(f"🤖 Using BotsForge service to solve Turnstile for {website_url}")

    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            # Create task
            create_payload = {
                "clientKey": api_key,
                "task": {
                    "type": "AntiTurnstileTaskProxyLess",
                    "websiteURL": website_url,
                    "websiteKey": website_key
                }
            }

            print("📤 Creating Turnstile solving task...")
            response = await client.post(f'{base_url}/createTask', json=create_payload)
            task_data = response.json()

            if task_data.get('status') != 'idle':
                print(f"❌ Failed to create task: {task_data}")
                return None

            task_id = task_data['taskId']
            print(f"✅ Task created with ID: {task_id}")

            # Poll for result
            print("⏳ Waiting for BotsForge to solve Turnstile...")
            for attempt in range(120):  # 2 minute timeout
                await asyncio.sleep(1)

                result_payload = {
                    "clientKey": api_key,
                    "taskId": task_id
                }

                response = await client.post(f'{base_url}/getTaskResult', json=result_payload)
                result = response.json()

                if result.get('status') == 'ready':
                    token = result['solution']['token']
                    print(f"🎉 BotsForge solved Turnstile! Token: {token[:20]}...")
                    return token
                elif result.get('status') == 'error':
                    print(f"❌ BotsForge error: {result.get('errorDescription')}")
                    return None

                # Progress indicator
                if attempt % 10 == 0 and attempt > 0:
                    print(f"⏳ Still waiting... ({attempt}s elapsed)")

            print("⏰ BotsForge service timeout")
            return None

    except Exception as e:
        print(f"❌ Error communicating with BotsForge service: {e}")
        return None

async def cloudflare_turnstile_solver_legacy(driver):
    """
    Legacy CDP shadow DOM approach (fast but less reliable)
    """
    print("🔧 Trying legacy CDP shadow DOM approach...")

    try:
        # Check if login button is already enabled
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
        is_disabled = await login_button.get_dom_attribute('disabled')

        if is_disabled is None:
            print("✅ Login button already enabled - no Turnstile required")
            return True

        # Look for Turnstile container
        turnstile_container = await driver.find_element(By.ID, "cf-turnstile", timeout=10)
        print("🎯 Found Turnstile container")

        # Wait for Turnstile to load
        await driver.sleep(5)

        # Try shadow DOM access
        try:
            shadow_host = await turnstile_container.find_element(By.CSS_SELECTOR, "div", timeout=5)
            shadow_root = await driver.execute_script("return arguments[0].shadowRoot", shadow_host)

            if shadow_root:
                print("📱 Accessed shadow root")
                iframe = await driver.execute_script("return arguments[0].querySelector('iframe')", shadow_root)

                if iframe:
                    print("🖼️ Found iframe in shadow DOM")
                    # Try gentle click on container
                    await turnstile_container.click()
                    print("👆 Clicked Turnstile container")

        except Exception as shadow_error:
            print(f"⚠️ Shadow DOM access failed: {shadow_error}")
            await turnstile_container.click()

        # Wait and check if successful
        await driver.sleep(8)

        for attempt in range(10):
            is_disabled = await login_button.get_dom_attribute('disabled')
            if is_disabled is None:
                print(f"✅ Legacy method successful after {attempt + 1} attempts!")
                return True
            await driver.sleep(2)

        print("❌ Legacy method failed")
        return False

    except Exception as e:
        print(f"❌ Legacy method error: {e}")
        return False

async def cloudflare_turnstile_solver(driver):
    """
    Hybrid Cloudflare Turnstile solver with BotsForge fallback.

    Two-tier approach:
    1. Fast CDP shadow DOM method (legacy)
    2. BotsForge visual recognition service (reliable fallback)
    """
    print("� Starting hybrid Cloudflare Turnstile verification...")

    # Tier 1: Try legacy method first (faster)
    print("🚀 Tier 1: Attempting fast legacy method...")
    if await cloudflare_turnstile_solver_legacy(driver):
        print("✅ Tier 1 successful - Turnstile solved with legacy method!")
        return True

    # Tier 2: Fallback to BotsForge service
    print("🔄 Tier 1 failed - falling back to BotsForge visual recognition...")

    try:
        # Extract Turnstile parameters from current page
        turnstile_data = await driver.execute_script("""
        const container = document.getElementById('cf-turnstile');
        if (container) {
            // Try multiple ways to get the site key
            let siteKey = container.getAttribute('data-sitekey') ||
                         container.querySelector('[data-sitekey]')?.getAttribute('data-sitekey');
            let iframeSrc = 'not found';

            // If not found, look in the shadow DOM
            if (!siteKey) {
                const shadowHost = container.querySelector('div');
                if (shadowHost && shadowHost.shadowRoot) {
                    const iframe = shadowHost.shadowRoot.querySelector('iframe');
                    if (iframe && iframe.src) {
                        iframeSrc = iframe.src;
                        console.log('Iframe src:', iframe.src);

                        // Extract site key from iframe src - Cloudflare Turnstile pattern
                        // Pattern: /rcv/[id]/[sitekey]/
                        const match = iframe.src.match(/\\/rcv\\/[^\\/]+\\/([^\\/]+)\\//);
                        if (match) {
                            siteKey = match[1];
                            console.log('Extracted site key:', siteKey);
                        } else {
                            // Fallback: try other patterns
                            const fallbackMatch = iframe.src.match(/[?&]sitekey=([^&]+)/);
                            if (fallbackMatch) siteKey = fallbackMatch[1];
                        }
                    }
                }
            }

            return {
                websiteURL: window.location.href,
                websiteKey: siteKey,
                containerFound: true,
                iframeSrc: iframeSrc
            };
        }
        return {containerFound: false};
        """)

        if not turnstile_data.get('containerFound'):
            print("❌ No Turnstile container found on page")
            return False

        if not turnstile_data.get('websiteKey'):
            print("❌ Could not extract Turnstile site key")
            return False

        print(f"📋 Extracted Turnstile data: {turnstile_data['websiteKey']}")

        # Solve with BotsForge service
        token = await solve_with_botsforge_service(
            turnstile_data['websiteURL'],
            turnstile_data['websiteKey']
        )

        if token:
            # Inject token into current page
            print("� Injecting token into page...")
            injection_result = await driver.execute_script(f"""
            // Try multiple ways to inject the token
            let injected = false;
            let message = '';

            // Method 1: Look for cf-turnstile-response input
            let input = document.querySelector('input[name="cf-turnstile-response"]');
            if (input) {{
                input.value = '{token}';
                input.dispatchEvent(new Event('change', {{bubbles: true}}));
                input.dispatchEvent(new Event('input', {{bubbles: true}}));
                injected = true;
                message += 'Found cf-turnstile-response input; ';
            }}

            // Method 2: Create the input if it doesn't exist
            if (!input) {{
                input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'cf-turnstile-response';
                input.value = '{token}';

                // Try to add it to the form
                const form = document.querySelector('form');
                if (form) {{
                    form.appendChild(input);
                    injected = true;
                    message += 'Created cf-turnstile-response input in form; ';
                }} else {{
                    document.body.appendChild(input);
                    injected = true;
                    message += 'Created cf-turnstile-response input in body; ';
                }}
            }}

            // Method 3: Try to trigger Turnstile callback directly
            if (window.turnstile && window.turnstile.render) {{
                try {{
                    // Simulate successful Turnstile completion
                    const container = document.getElementById('cf-turnstile');
                    if (container) {{
                        const event = new CustomEvent('cf-turnstile-callback', {{
                            detail: {{ token: '{token}' }}
                        }});
                        container.dispatchEvent(event);
                        message += 'Triggered turnstile callback; ';
                    }}
                }} catch (e) {{
                    message += 'Callback trigger failed: ' + e.message + '; ';
                }}
            }}

            // Method 4: Set global variables that Turnstile might check
            window.turnstileToken = '{token}';
            window.cfTurnstileResponse = '{token}';

            return {{
                success: injected,
                message: message || 'No injection methods succeeded',
                tokenLength: '{token}'.length
            }};
            """)

            print(f"💉 Token injection result: {injection_result}")

            # Wait for page to process the token
            await driver.sleep(3)

            # Check if login button is now enabled
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
            is_disabled = await login_button.get_dom_attribute('disabled')

            if is_disabled is None:
                print("✅ Tier 2 successful - BotsForge solved Turnstile!")
                return True
            else:
                print("⚠️ Token injected but login button still disabled")
                return False
        else:
            print("❌ BotsForge failed to solve Turnstile")
            return False

    except Exception as e:
        print(f"❌ Tier 2 error: {e}")
        return False



async def main():
    options = webdriver.ChromeOptions()
    proxy = "*************:8080"

    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    #C:\ProgramData\Microsoft\Windows\Start Menu\Programs
    async with webdriver.Chrome(options=options) as driver:

        # Force all shadow DOMs to be open instead of closed (Stack Overflow solution)
        await driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {'source': """
        Element.prototype._attachShadow = Element.prototype.attachShadow;
        Element.prototype.attachShadow = function () {
            return this._attachShadow( { mode: "open" } );
        };
        """})
        print("🔧 Configured shadow DOM override for Turnstile access")

        #await driver.set_single_proxy(proxy)
        await driver.get('https://ship.pirateship.com/')
        print("🌐 Navigated to PirateShip login page")

        # Enter login credentials
        email_input = await driver.find_element(By.CSS_SELECTOR, "input[name='email']", timeout=element_timeout)
        await email_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await email_input.send_keys(email)
        print("📧 Entered email")

        password_input = await driver.find_element(By.CSS_SELECTOR, "input[name='password']", timeout=element_timeout)
        await password_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await password_input.send_keys(password)
        print("🔑 Entered password")

        await driver.sleep(2)  # Allow page to settle

        # Handle Cloudflare Turnstile verification BEFORE clicking login
        turnstile_result = await cloudflare_turnstile_solver(driver)
        print(f"🎯 Turnstile verification result: {turnstile_result}")

        if not turnstile_result:
            print("❌ Turnstile verification failed. Attempting login anyway...")
            # Don't quit immediately - sometimes login works despite Turnstile issues

        # Click the login button
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
        await login_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        print("🚀 Clicked login button")

        # Wait for login to complete and check result
        await driver.sleep(8)

        try:
            current_url = await driver.current_url
            print(f"📍 Current URL after login: {current_url}")

            if "ship" in current_url or "dashboard" in current_url or current_url != "https://ship.pirateship.com/":
                print("✅ Login successful! Proceeding to shipping functionality...")
            else:
                print("⚠️ Login may have failed - still on login page")
                # Try to continue anyway - sometimes the URL doesn't change immediately

        except Exception as url_error:
            print(f"⚠️ Could not check URL after login: {url_error}")
            print("🔄 Continuing with automation...")

        print("🎉 Login process completed - proceeding to shipping automation")
        await driver.sleep(3)
        #await driver.get('https://ship.pirateship.com/ship') # Need to go to default frame it keeps crashing here  !!!
        #await driver.switch_to.default_content() #maybe open new window or something idk
        #temp = await driver.get_screenshot_as_png()
        #await driver.refresh()
                

        # Locate the <a> element with class 'action-btn' and click it
        action_button = await driver.find_element(By.CLASS_NAME, "action-btn-label", timeout=element_timeout)
        await action_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        
        # Locate the <a> element with id 'copypaste-link' and click it
        paste_address_link = await driver.find_element(By.ID, "copypaste-link", timeout=element_timeout)
        await paste_address_link.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        
        textarea = await driver.find_element(By.ID, "shipment-copypaste", timeout=element_timeout)
        await  textarea.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)  # Click the textarea to focus
        await textarea.send_keys(address) 
        
        # Click and type the email
        email_input = await driver.find_element(By.ID, "shipment-email", timeout=element_timeout)
        await email_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await email_input.send_keys(send_email)

        # Click and type the phone number
        phone_input = await driver.find_element(By.ID, "shipment-phone", timeout=element_timeout)
        await phone_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await phone_input.send_keys(phone_number)

        # Click and type the recipient's name
        name_input = await driver.find_element(By.ID, "shipment-full-name", timeout=element_timeout)
        await name_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await name_input.send_keys(recipient_name)

        # Click and type the company name
        company_input = await driver.find_element(By.ID, "shipment-company", timeout=element_timeout)
        await company_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await company_input.send_keys(company_name)
        
        time.sleep(2)  # Adjust sleep time or use WebDriverWait for better handling

        # Select the specific option (for example, USPS Priority Mail Small Flat Rate Box)
        
        # Click and type the length
        length_input = await driver.find_element(By.ID, "configuration-key-length", timeout=element_timeout)
        await length_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=True,move_to=True)
        await length_input.send_keys(length)

        # Click and type the width
        width_input = await driver.find_element(By.ID, "configuration-key-width", timeout=element_timeout)
        await width_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await width_input.send_keys(width)

        # Click and type the height
        height_input = await driver.find_element(By.ID, "configuration-key-height", timeout=element_timeout)
        await height_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await height_input.send_keys(height)

        # Click and type the weight in pounds
        pounds_input = await driver.find_element(By.ID, "configuration-key-weight-pounds", timeout=element_timeout)
        await pounds_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await pounds_input.send_keys(pounds)

        # Check if the ounces input exists and interact with it
        try:
            ounces_input = await driver.find_element(By.ID, "configuration-key-weight-ounces", timeout=element_timeout)
            await ounces_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
            await ounces_input.send_keys(ounces)
        except:
            print("Ounces input field not found.")

        # Check if hazardous_materials is true and interact with the checkbox
        if hazardous_materials:
            hazardous_materials_checkbox = await driver.find_element(By.CSS_SELECTOR, "label.checkbox-label.checkbox input.toggle-checkbox.hazardous_materials_flag", timeout=element_timeout)
            await hazardous_materials_checkbox.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
            
        # Click the "Get Rates" button
        submit_button = await driver.find_element(By.XPATH, "//div[@class='col-md-12']//button[@id='origin-submit']", timeout=element_timeout)
        await submit_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=True,move_to=True)

        print("Clicked")
        time.sleep(2) 
        
        print("Best Selected by Default")
        
        # Locate and click the button
        buy_button = await driver.find_element(By.XPATH, "//button[@id='buy-button']", timeout=element_timeout)
        await buy_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=True,move_to=True)
        
        #From here you need to setup payment !!!
        print("Done")
        
        await driver.sleep(30) 
        await driver.quit()    

asyncio.run(main())


'''
async def click_checkbox(driver):
    await driver.switch_to.default_content()
    print("Yup")

    await driver.switch_to.frame(driver.find_element(By.XPATH, ".//iframe[@title='reCAPTCHA']"))
    print("Yup")

    capcha = await driver.find_element(By.CSS_SELECTOR, "#recaptcha-anchor > div.recaptcha-checkbox-border")
    await capcha.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
    print("Yup")
    
    await driver.switch_to.default_content()
    print("Yup")
    await driver.sleep(5)
    

async def request_audio_version(driver):
    await driver.switch_to.default_content()
    await driver.switch_to.frame(driver.find_element(By.XPATH, ".//iframe[@title='recaptcha challenge expires in two minutes']"))
    audio = await driver.find_element(By.ID, "recaptcha-audio-button", timeout=element_timeout)
    await audio.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)


async def solve_audio_captcha(driver):
    text = await transcribe(driver.find_element(By.ID, "audio-source").get_attribute('src'))
    solve_audio = await driver.find_element(By.ID, "audio-response", timeout=element_timeout)
    await solve_audio.send_keys(text)
    verify_button = await driver.find_element(By.ID, "recaptcha-verify-button", timeout=element_timeout)
    await verify_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)

async def captcha(driver):
    time.sleep(5)
    await click_checkbox(driver)
    time.sleep(5)
    await request_audio_version(driver)
    time.sleep(5)
    await solve_audio_captcha(driver)
    time.sleep(10)
            

'''
'''
        # Create temporary directory and temporary files
        tmp_dir = current_directory = os.getcwd()

        id_ = uuid.uuid4().hex

        mp3_file, wav_file = os.path.join(tmp_dir, f'{id_}_tmp.mp3'), os.path.join(tmp_dir, f'{id_}_tmp.wav')
        
        tmp_files = {mp3_file, wav_file}
                            
        print(tmp_files)
            # Convert MP3 to WAV format for compatibility with speech recognizer APIs
            AudioSegment.from_mp3(mp3_file).export(wav_file, format='wav')

            recognizer = sr.Recognizer()
            # Disable dynamic energy threshold to avoid failed reCAPTCHA audio transcription due to static noise
            recognizer.dynamic_energy_threshold = False

            with sr.AudioFile(wav_file) as source:
                audio = recognizer.listen(source)

                try:
                    recognized_text = _service.recognize(recognizer, audio, language)

                except sr.UnknownValueError:
                    raise RecaptchaException('Speech recognition API could not understand audio, try again')

            # Clean up all temporary files
            for path in tmp_files:
                if os.path.exists(path):
                    os.remove(path)
'''
    #except Exception as e:
        #print("Error occurred:", e)

    #finally:
    #   await driver.quit()
    
    
'''
puzzle_element = await driver.find_element(By.XPATH, '//*[@id="portal-game-toolbar"]/div/ul/div[2]/li[2]/ul/li[3]/button', timeout=element_timeout)
            await puzzle_element.click()
            
            #//*[@id="portal-game-modals"]/div/div/div[2]/article/div/button[2]
            reveal_two_element = await driver.find_element(By.XPATH, '//*[@id="portal-game-modals"]/div/div/div[2]/article/div/button[2]', timeout=element_timeout)
            await reveal_two_element.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)

            back_button_puzzle = await driver.find_element(By.CLASS_NAME, 'pz-icon pz-icon-close', timeout=element_timeout)
            await back_button_puzzle.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)

            #await driver.get('https://www.nytimes.com/crosswords/game/mini')

            #await driver.sleep(2.5)
            
            #button_element = await driver.find_element(By.CSS_SELECTOR, 'button.xwd__modal--subtle-button', timeout=element_timeout)
            #wait button_element.click() 

            #await driver.save_snapshot('test.mhtml')

            page_source = await driver.page_source

            #soup = BeautifulSoup(page_source, "html.parser")
            
            temp = await html_parser(BeautifulSoup(page_source, "html.parser"))
'''


# Iterate through each option and print its text and description
'''
option_text = "USPS Priority Mail Small Flat Rate Box"
dropdown_trigger = await driver.find_element(By.CSS_SELECTOR, "YOUR_DROPDOWN_TRIGGER_SELECTOR", timeout=element_timeout)
await driver.location_once_scrolled_into_view(dropdown_trigger)
await dropdown_trigger.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)

# Find the dropdown menu
dropdown_menu = await driver.find_element(By.CSS_SELECTOR, "ul.dd-options", timeout=element_timeout)
await dropdown_menu.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=True,move_to=False)

options = await dropdown_menu.find_elements(By.CSS_SELECTOR, "li a", timeout=element_timeout)
for option in options: 
    label = await option.find_element(By.CSS_SELECTOR, "label.dd-option-text", timeout=element_timeout).text #Might not work get rid of text 
    description = await option.find_element(By.CSS_SELECTOR, "small.dd-option-description", timeout=element_timeout).text
    print(f"Option: {label}")
    print(f"Description: {description}")
    print("-" * 40)

    # Check if this is the desired option and select it
    if label == option_text:
        await option.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=True,move_to=False)
        break
'''