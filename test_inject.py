import asyncio
import httpx
import time

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

##########################################################
# 1️⃣ Async function to solve Turnstile using BotsForge
##########################################################
async def solve_with_botsforge_service(website_url, website_key):
    api_key = "pirateship-turnstile-solver-2024"
    base_url = "http://localhost:5033"

    print(f"🤖 Using BotsForge service to solve Turnstile for {website_url}")

    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            # Create task
            create_payload = {
                "clientKey": api_key,
                "task": {
                    "type": "AntiTurnstileTaskProxyLess",
                    "websiteURL": website_url,
                    "websiteKey": website_key
                }
            }
            print("📤 Creating Turnstile solving task...")
            response = await client.post(f'{base_url}/createTask', json=create_payload)
            task_data = response.json()

            if task_data.get('status') != 'idle':
                print(f"❌ Failed to create task: {task_data}")
                return None

            task_id = task_data['taskId']
            print(f"✅ Task created with ID: {task_id}")

            # Poll for result
            print("⏳ Waiting for BotsForge to solve Turnstile...")
            for attempt in range(120):
                await asyncio.sleep(1)

                result_payload = {"clientKey": api_key, "taskId": task_id}
                response = await client.post(f'{base_url}/getTaskResult', json=result_payload)
                result = response.json()

                if result.get('status') == 'ready':
                    token = result['solution']['token']
                    print(f"🎉 BotsForge solved Turnstile! Token: {token[:20]}...")
                    return token
                elif result.get('status') == 'error':
                    print(f"❌ BotsForge error: {result.get('errorDescription')}")
                    return None

                if attempt % 10 == 0 and attempt > 0:
                    print(f"⏳ Still waiting... ({attempt}s elapsed)")

            print("⏰ BotsForge service timeout")
            return None

    except Exception as e:
        print(f"❌ Error communicating with BotsForge service: {e}")
        return None

##########################################################
# 2️⃣ Main Selenium flow
##########################################################
def main():
    # Optional: use Brave or Chrome
    options = webdriver.ChromeOptions()
    # options.add_argument("--headless=new")  # modern headless
    # options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"

    driver = webdriver.Chrome(options=options)

    try:
        ##########################################################
        # 2.1 Go to PirateShip login page
        ##########################################################
        print("🌐 Navigating to PirateShip login page...")
        driver.get("https://ship.pirateship.com/")
        time.sleep(5)

        ##########################################################
        # 2.2 Try to extract site key
        ##########################################################
        print("🔍 Trying to extract Turnstile site key...")
        turnstile_data = driver.execute_script("""
            const container = document.getElementById('cf-turnstile');
            if (container) {
                let siteKey = container.getAttribute('data-sitekey') || null;
                return { websiteKey: siteKey, containerFound: true };
            }
            return { containerFound: false };
        """)
        if not turnstile_data.get('containerFound') or not turnstile_data.get('websiteKey'):
            print("⚠️ Could not extract site key—using known fallback.")
            site_key = "0x4AAAAAAAyYdva70TquiHgp"
        else:
            site_key = turnstile_data['websiteKey']

        print(f"✅ Using site key: {site_key}")

        ##########################################################
        # 2.3 Solve Turnstile with BotsForge
        ##########################################################
        print("🤖 Solving Turnstile with BotsForge...")
        token = asyncio.run(solve_with_botsforge_service(
            driver.current_url,
            site_key
        ))

        if not token:
            print("❌ Failed to solve Turnstile. Exiting.")
            return

        print(f"✅ Got Turnstile Token: {token[:20]}...")

        ##########################################################
        # 2.4 Enter email and password
        ##########################################################
        print("📧 Entering credentials...")
        email_input = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, "input[name='email']"))
        )
        password_input = driver.find_element(By.CSS_SELECTOR, "input[name='password']")

        # Replace these with actual credentials
        email_input.send_keys("YOUR_EMAIL")
        password_input.send_keys("YOUR_PASSWORD")
        print("✅ Credentials entered")

        ##########################################################
        # 2.5 Force render by simulating user actions
        ##########################################################
        print("⚡ Forcing Turnstile render with focus/blur/click...")
        driver.execute_script("""
            document.querySelector('input[name="email"]').focus();
            document.querySelector('input[name="email"]').blur();
            document.querySelector('input[name="password"]').focus();
            document.querySelector('input[name="password"]').blur();
        """)
        time.sleep(2)

        try:
            submit_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            submit_button.click()
            print("✅ Clicked submit once to trigger validation")
        except Exception as e:
            print(f"⚠️ Couldn't click submit button: {e}")

        time.sleep(5)

        ##########################################################
        # 2.6 Wait for the hidden input to appear
        ##########################################################
        print("⏳ Waiting for cf-turnstile-response input to appear...")
        for attempt in range(30):
            time.sleep(1)
            found = driver.execute_script("""
                return !!document.querySelector('[name="cf-turnstile-response"]');
            """)
            if found:
                print(f"✅ Found cf-turnstile-response input after {attempt+1}s")
                break
            if attempt % 5 == 0 and attempt > 0:
                print(f"⏳ Still waiting... ({attempt}s)")
        else:
            print("❌ Input never appeared—can't inject.")
            return

        ##########################################################
        # 2.7 Inject the token
        ##########################################################
        print("💉 Injecting Turnstile token...")
        injection_result = driver.execute_script("""
            let input = document.querySelector('[name="cf-turnstile-response"]');
            if (input) {
                input.value = arguments[0];
                input.dispatchEvent(new Event("input", { bubbles: true }));
                input.dispatchEvent(new Event("change", { bubbles: true }));
                return { success: true };
            } else {
                // Create input if it somehow doesn't exist
                input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'cf-turnstile-response';
                input.value = arguments[0];
                document.querySelector('form').appendChild(input);
                return { success: 'created' };
            }
        """, token)
        print(f"✅ Injection result: {injection_result}")

        ##########################################################
        # 2.8 Submit the form for real
        ##########################################################
        print("🚀 Final submit...")
        try:
            submit_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            if not submit_button.is_enabled():
                driver.execute_script("""
                    const btn = document.querySelector('button[type="submit"]');
                    btn.disabled = false;
                    btn.removeAttribute('disabled');
                """)
            submit_button.click()
            print("✅ Clicked submit button")
        except Exception as e:
            print(f"❌ Error clicking submit: {e}")

        ##########################################################
        # 2.9 Wait and check
        ##########################################################
        print("⏳ Waiting for login...")
        time.sleep(10)
        print(f"📍 Current URL: {driver.current_url}")

        if "login" not in driver.current_url.lower():
            print("✅ LOGIN SUCCESSFUL!")
        else:
            print("❌ Still on login page—might have failed.")

    except Exception as e:
        print(f"❌ Error in main: {e}")

    finally:
        print("🔧 Closing browser...")
        driver.quit()

##########################################################
# ENTRYPOINT
##########################################################
if __name__ == "__main__":
    main()
