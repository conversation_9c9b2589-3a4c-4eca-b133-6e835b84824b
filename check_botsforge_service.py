#!/usr/bin/env python3
"""
Script to check if BotsForge service is running and healthy
"""
import asyncio
import httpx

async def check_service_health():
    """Check if BotsForge service is running and responsive"""
    base_url = "http://localhost:5033"
    api_key = "pirateship-turnstile-solver-2024"
    
    print("🔍 Checking BotsForge service health...")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Try to create a test task to verify service is working
            test_payload = {
                "clientKey": api_key,
                "task": {
                    "type": "AntiTurnstileTaskProxyLess",
                    "websiteURL": "https://example.com",
                    "websiteKey": "test-key"
                }
            }
            
            print(f"📡 Connecting to {base_url}...")
            response = await client.post(f'{base_url}/createTask', json=test_payload)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Service is running! Response: {result}")
                
                if result.get('status') == 'idle':
                    print("✅ Service is healthy and ready to accept tasks")
                    return True
                else:
                    print(f"⚠️ Service responded but status is: {result.get('status')}")
                    return False
            else:
                print(f"❌ Service returned status code: {response.status_code}")
                return False
                
    except httpx.ConnectError:
        print("❌ Cannot connect to BotsForge service")
        print("💡 Make sure the service is running on localhost:5033")
        return False
    except Exception as e:
        print(f"❌ Error checking service: {e}")
        return False

async def main():
    print("🔥 BotsForge Service Health Check")
    print("=" * 40)
    
    is_healthy = await check_service_health()
    
    if is_healthy:
        print("\n🎉 BotsForge service is ready for Turnstile solving!")
    else:
        print("\n❌ BotsForge service is not available")
        print("📋 To start the service, run: python start_botsforge_service.py")

if __name__ == "__main__":
    asyncio.run(main())
