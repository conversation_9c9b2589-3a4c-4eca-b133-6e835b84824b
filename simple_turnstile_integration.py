"""
Simple and Direct Turnstile Integration
Uses the working BotsForge Browser class directly for reliable Turnstile solving.
"""

import asyncio
import sys
import os
from typing import Optional
from loguru import logger

# Import for Selenium integration
try:
    from selenium_driverless.types.by import By
except ImportError:
    # Fallback if selenium-driverless not available
    class By:
        CSS_SELECTOR = "css selector"

# Add BotsForge to path
sys.path.insert(0, 'BotsForge-CloudFlare')

from browser import Browser
from models import CaptchaTask


class SimpleTurnstileSolver:
    """
    Simple wrapper around the working BotsForge Browser class.
    Provides easy integration with existing automation workflows.
    """
    
    def __init__(self):
        self.browser = None
    
    async def solve_turnstile(self, website_url: str, website_key: str, action: str = '') -> Optional[str]:
        """
        Solve Cloudflare Turnstile using the proven BotsForge Browser class.
        
        Args:
            website_url: The URL where the Turnstile is located
            website_key: The site key for the Turnstile
            action: Optional action parameter
            
        Returns:
            The solved token or None if failed
        """
        try:
            logger.info(f"🎯 Solving Turnstile for {website_url}")
            
            # Create task
            task = CaptchaTask(
                id=f"task-{int(asyncio.get_event_loop().time())}",
                type='AntiTurnstileTaskProxyLess',
                websiteURL=website_url,
                websiteKey=website_key
            )
            
            # Create browser instance
            self.browser = Browser()
            
            # Solve the captcha
            token = await self.browser.solve_captcha(task)
            
            if token:
                logger.info(f"✅ Turnstile solved successfully: {token[:50]}...")
                return token
            else:
                logger.warning("❌ Failed to solve Turnstile")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error solving Turnstile: {e}")
            return None


async def solve_turnstile_simple(website_url: str, website_key: str, action: str = '') -> Optional[str]:
    """
    Simple function to solve Turnstile - one-shot usage.
    
    Args:
        website_url: The URL where the Turnstile is located
        website_key: The site key for the Turnstile
        action: Optional action parameter
        
    Returns:
        The solved token or None if failed
    """
    solver = SimpleTurnstileSolver()
    return await solver.solve_turnstile(website_url, website_key, action)


async def inject_turnstile_token(driver, token: str) -> bool:
    """
    Inject a solved Turnstile token into a Selenium page.
    
    Args:
        driver: Selenium driver instance
        token: The solved Turnstile token
        
    Returns:
        True if injection was successful, False otherwise
    """
    try:
        logger.info(f"💉 Injecting Turnstile token: {token[:50]}...")
        
        # Inject token using JavaScript
        injection_script = f"""
        // Set the cf-turnstile-response input
        const responseInput = document.querySelector('input[name="cf-turnstile-response"]');
        if (responseInput) {{
            responseInput.value = '{token}';
            responseInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
            responseInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
        }}
        
        // Trigger callback if it exists
        if (typeof window.tsCallback === 'function') {{
            window.tsCallback('{token}');
        }}
        
        // Try other common callback names
        const callbacks = ['onCaptchaSuccess', 'turnstileCallback', 'cfCallback'];
        callbacks.forEach(cb => {{
            if (typeof window[cb] === 'function') {{
                window[cb]('{token}');
            }}
        }});
        
        return responseInput ? true : false;
        """
        
        result = await driver.execute_script(injection_script)
        
        if result:
            logger.info("✅ Token injection successful")
            return True
        else:
            logger.warning("⚠️ Token injection may have failed - no response input found")
            return False
            
    except Exception as e:
        logger.error(f"❌ Token injection failed: {e}")
        return False


async def enhanced_pirateship_turnstile_solver(driver) -> bool:
    """
    Enhanced Turnstile solver specifically for PirateShip.
    Uses the proven BotsForge Browser class for reliable solving.
    
    Args:
        driver: Selenium driver instance
        
    Returns:
        True if Turnstile was solved successfully, False otherwise
    """
    try:
        logger.info("🔒 Starting enhanced PirateShip Turnstile solving...")
        
        # Check if login button is already enabled
        try:
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
            is_disabled = await login_button.get_dom_attribute('disabled')
            
            if is_disabled is None:
                logger.info("✅ Login button already enabled - no Turnstile required")
                return True
        except:
            pass
        
        # Extract Turnstile parameters from the page
        try:
            turnstile_data = await driver.execute_script("""
            return {
                websiteKey: document.querySelector('#cf-turnstile')?.getAttribute('data-sitekey') ||
                           document.querySelector('[data-sitekey]')?.getAttribute('data-sitekey'),
                websiteURL: window.location.href,
                action: document.querySelector('#cf-turnstile')?.getAttribute('data-action') || '',
                turnstileExists: !!document.querySelector('#cf-turnstile'),
                allSiteKeys: Array.from(document.querySelectorAll('[data-sitekey]')).map(el => el.getAttribute('data-sitekey')),
                iframeCount: document.querySelectorAll('#cf-turnstile iframe').length
            };
            """)
            
            if not turnstile_data.get('turnstileExists'):
                logger.info("ℹ️ No Turnstile detected on page")
                return True
                
            if not turnstile_data.get('websiteKey'):
                logger.warning("⚠️ Turnstile detected but no site key found")
                return False
                
            logger.info(f"🔑 Extracted Turnstile key: {turnstile_data['websiteKey']}")
            
            # Solve using the proven BotsForge method
            token = await solve_turnstile_simple(
                website_url=turnstile_data['websiteURL'],
                website_key=turnstile_data['websiteKey'],
                action=turnstile_data.get('action', '')
            )
            
            if token:
                # Inject the token
                injection_success = await inject_turnstile_token(driver, token)
                
                if injection_success:
                    # Wait for page to process the token
                    await driver.sleep(2)
                    
                    # Check if login button is now enabled
                    try:
                        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
                        is_disabled = await login_button.get_dom_attribute('disabled')
                        
                        if is_disabled is None:
                            logger.info("🎉 Turnstile solved successfully - login button enabled!")
                            return True
                        else:
                            logger.warning("⚠️ Login button still disabled after token injection")
                            return False
                    except:
                        logger.warning("⚠️ Could not verify login button state")
                        return True  # Assume success if we can't check
                else:
                    logger.error("❌ Token injection failed")
                    return False
            else:
                logger.error("❌ Failed to obtain Turnstile token")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error extracting Turnstile data: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Enhanced Turnstile solver failed: {e}")
        return False


if __name__ == "__main__":
    async def test_simple_solver():
        """Test the simple solver"""
        logger.info("🧪 Testing Simple Turnstile Solver")
        
        # Test with known working site
        token = await solve_turnstile_simple(
            website_url="https://faucet.sonic.game",
            website_key="0x4AAAAAAAc6HG1RMG_8EHSC"
        )
        
        if token:
            logger.info(f"✅ Test successful: {token[:50]}...")
            return True
        else:
            logger.warning("❌ Test failed")
            return False
    
    # Run test
    result = asyncio.run(test_simple_solver())
    print(f"\n🏁 Test Result: {'SUCCESS' if result else 'FAILED'}")
