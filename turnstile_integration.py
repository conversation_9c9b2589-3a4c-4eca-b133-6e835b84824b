"""
Turnstile Integration Module
Integrates BotsForge-CloudFlare direct solving capabilities with existing automation workflows.
"""

import asyncio
import sys
import os
from typing import Optional, Union
from loguru import logger

# Add BotsForge-CloudFlare to path for imports
botsforge_path = os.path.join(os.path.dirname(__file__), 'BotsForge-CloudFlare')
sys.path.insert(0, botsforge_path)

try:
    from direct_turnstile_solver import DirectTurnstileSolver
except ImportError as e:
    logger.error(f"Failed to import DirectTurnstileSolver: {e}")
    logger.info("Make sure BotsForge-CloudFlare directory exists and contains direct_turnstile_solver.py")
    raise


class TurnstileIntegration:
    """
    High-level integration class for Turnstile solving in automation workflows.
    Provides both standalone and existing-page solving capabilities.
    """
    
    def __init__(self, headless: bool = False, proxy: Optional[str] = None):
        self.headless = headless
        self.proxy = proxy
        self.solver = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.solver = DirectTurnstileSolver(headless=self.headless, proxy=self.proxy)
        await self.solver.initialize()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.solver:
            await self.solver.cleanup()
    
    async def solve_turnstile_standalone(self, website_url: str, website_key: str, action: str = '') -> Optional[str]:
        """
        Solve Turnstile in a standalone browser session.
        
        Args:
            website_url: The URL where the Turnstile is located
            website_key: The site key for the Turnstile
            action: Optional action parameter
            
        Returns:
            The solved token or None if failed
        """
        if not self.solver:
            raise RuntimeError("TurnstileIntegration not initialized. Use as async context manager.")
            
        return await self.solver.solve_turnstile(website_url, website_key, action)
    
    async def solve_turnstile_on_page(self, page, website_key: str, action: str = '') -> Optional[str]:
        """
        Solve Turnstile on an existing Playwright page.
        Perfect for integration with existing automation workflows.
        
        Args:
            page: Existing Playwright page object
            website_key: The site key for the Turnstile
            action: Optional action parameter
            
        Returns:
            The solved token or None if failed
        """
        if not self.solver:
            raise RuntimeError("TurnstileIntegration not initialized. Use as async context manager.")
            
        return await self.solver.solve_on_existing_page(page, website_key, action)
    
    async def inject_token_and_continue(self, page, token: str, callback_name: str = 'tsCallback') -> bool:
        """
        Inject a solved token into the page and trigger callbacks.
        
        Args:
            page: Playwright page object
            token: The solved Turnstile token
            callback_name: Name of the callback function to trigger
            
        Returns:
            True if injection was successful, False otherwise
        """
        try:
            logger.info(f"💉 Injecting token: {token[:50]}...")
            
            # Inject token into cf-turnstile-response input
            injection_script = f"""
            // Find and set the cf-turnstile-response input
            const responseInput = document.querySelector('input[name="cf-turnstile-response"]');
            if (responseInput) {{
                responseInput.value = '{token}';
                console.log('Token injected into cf-turnstile-response input');
            }}
            
            // Trigger callback if it exists
            if (typeof window.{callback_name} === 'function') {{
                window.{callback_name}('{token}');
                console.log('Callback {callback_name} triggered');
            }}
            
            // Trigger any other common callback names
            const callbacks = ['onCaptchaSuccess', 'turnstileCallback', 'cfCallback'];
            callbacks.forEach(cb => {{
                if (typeof window[cb] === 'function') {{
                    window[cb]('{token}');
                    console.log('Callback ' + cb + ' triggered');
                }}
            }});
            
            // Dispatch change events
            if (responseInput) {{
                responseInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                responseInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
            }}
            
            return true;
            """
            
            result = await page.evaluate(injection_script)
            
            # Wait a moment for the page to process the token
            await asyncio.sleep(1)
            
            logger.info("✅ Token injection completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Token injection failed: {e}")
            return False


# Convenience functions for quick integration
async def solve_turnstile_quick(website_url: str, website_key: str, action: str = '', 
                               headless: bool = False, proxy: Optional[str] = None) -> Optional[str]:
    """
    Quick standalone Turnstile solving function.
    
    Args:
        website_url: The URL where the Turnstile is located
        website_key: The site key for the Turnstile
        action: Optional action parameter
        headless: Whether to run browser in headless mode
        proxy: Optional proxy string (format: "host:port" or "user:pass@host:port")
        
    Returns:
        The solved token or None if failed
    """
    async with TurnstileIntegration(headless=headless, proxy=proxy) as integration:
        return await integration.solve_turnstile_standalone(website_url, website_key, action)


async def solve_turnstile_on_existing_page(page, website_key: str, action: str = '') -> Optional[str]:
    """
    Quick function to solve Turnstile on an existing page.
    
    Args:
        page: Existing Playwright page object
        website_key: The site key for the Turnstile
        action: Optional action parameter
        
    Returns:
        The solved token or None if failed
    """
    # Create a temporary solver instance
    solver = DirectTurnstileSolver()
    try:
        # We don't need to initialize the solver since we're using an existing page
        return await solver.solve_on_existing_page(page, website_key, action)
    finally:
        # No cleanup needed since we didn't initialize our own browser
        pass


# Enhanced integration for your existing workflow
async def enhanced_turnstile_solver(driver_or_page, website_url: str = None, website_key: str = None) -> bool:
    """
    Enhanced Turnstile solver that integrates with your existing automation workflow.
    Automatically detects Turnstile parameters and solves it.
    
    Args:
        driver_or_page: Selenium driver or Playwright page object
        website_url: Optional website URL (will be detected if not provided)
        website_key: Optional website key (will be detected if not provided)
        
    Returns:
        True if Turnstile was solved successfully, False otherwise
    """
    try:
        logger.info("🔒 Starting enhanced Turnstile solving...")
        
        # Detect if we're working with Selenium or Playwright
        is_playwright = hasattr(driver_or_page, 'evaluate')
        
        if is_playwright:
            page = driver_or_page
            
            # Get current URL if not provided
            if not website_url:
                website_url = page.url
                
            # Extract Turnstile data if not provided
            if not website_key:
                turnstile_data = await page.evaluate("""
                () => {
                    const turnstileElement = document.querySelector('[data-sitekey]') || 
                                           document.querySelector('.cf-turnstile') ||
                                           document.querySelector('#cf-turnstile');
                    
                    if (turnstileElement) {
                        return {
                            websiteKey: turnstileElement.getAttribute('data-sitekey') || 
                                       turnstileElement.getAttribute('sitekey'),
                            action: turnstileElement.getAttribute('data-action') || ''
                        };
                    }
                    return null;
                }
                """)
                
                if turnstile_data and turnstile_data.get('websiteKey'):
                    website_key = turnstile_data['websiteKey']
                    logger.info(f"🎯 Detected Turnstile key: {website_key}")
                else:
                    logger.warning("⚠️ Could not detect Turnstile parameters")
                    return False
            
            # Solve the Turnstile
            token = await solve_turnstile_on_existing_page(page, website_key)
            
            if token:
                # Inject token and trigger callbacks
                async with TurnstileIntegration() as integration:
                    success = await integration.inject_token_and_continue(page, token)
                    
                if success:
                    logger.info("✅ Enhanced Turnstile solving completed successfully")
                    return True
                    
        else:
            # Handle Selenium driver case
            logger.warning("🔄 Selenium driver detected - converting to Playwright recommended")
            # You could add Selenium-specific handling here if needed
            return False
            
    except Exception as e:
        logger.error(f"❌ Enhanced Turnstile solving failed: {e}")
        
    return False


if __name__ == "__main__":
    # Example usage
    async def test_integration():
        """Test the integration with a sample Turnstile"""
        
        # Test standalone solving
        token = await solve_turnstile_quick(
            website_url="https://faucet.sonic.game",
            website_key="0x4AAAAAAAc6HG1RMG_8EHSC",
            headless=False
        )
        
        if token:
            print(f"✅ Standalone test successful: {token[:50]}...")
        else:
            print("❌ Standalone test failed")
    
    # Run test
    asyncio.run(test_integration())
