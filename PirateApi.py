import time
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
import asyncio
import requests
import whisper
import re

# Import the simple, working BotsForge Turnstile integration
from simple_turnstile_integration import enhanced_pirateship_turnstile_solver, solve_turnstile_simple

email = "<EMAIL>"
password = "sacramento209%%"
address = "8391 Folsom Blvd, Sacramento, CA 95826"
send_email = email
phone_number = "9168352323" #Must be valid
recipient_name = "jake"
company_name = "jake corp"
length = "10"
width = "20"
height = "5"
pounds = "5"
ounces = ""
hazardous_materials = True  
element_timeout = 15

options = {
    '1': 'Best',
    '2': 'Cheapest',
    '3': 'USPS',
    '4': 'UPS'
}
selection = options['1']

#Problems marked with !!! ctrl F to them 

model = whisper.load_model("base")


async def clean_text(text):
    # Keep only alphanumeric characters and spaces
    return re.sub(r'[^a-zA-Z0-9\s]', '', text).strip()

async def transcribe(url):
    with open('.temp', 'wb') as f:
        f.write(requests.get(url).content)
        
    result = model.transcribe('.temp')
    cleaned_text = await clean_text(result["text"])
    print("AI: ", result["text"].strip(), " -> ", cleaned_text)
    return cleaned_text

async def cloudflare_turnstile_solver(driver):
    """
    Enhanced Cloudflare Turnstile solver using the proven BotsForge Browser class.
    This uses the working BotsForge integration that successfully generates tokens.
    """
    print("🔒 Starting Cloudflare Turnstile verification with proven BotsForge method...")

    try:
        # Use the enhanced PirateShip-specific solver
        success = await enhanced_pirateship_turnstile_solver(driver)

        if success:
            print("✅ Turnstile solved successfully with BotsForge Browser class!")
            return True
        else:
            print("❌ BotsForge Turnstile solving failed")
            return False

    except Exception as e:
        print(f"❌ Error in Turnstile solver: {e}")
        # Fallback: check if login is possible anyway
        try:
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
            is_disabled = await login_button.get_dom_attribute('disabled')
            if is_disabled is None:
                print("✅ Login possible despite Turnstile error")
                return True
        except:
            pass
        return False


# Keep the old reCAPTCHA solver as backup (renamed)
async def recaptcha_solver_backup(driver):
    """
    Legacy reCAPTCHA solver - kept as backup in case site reverts to reCAPTCHA
    """
    print("🔄 Using legacy reCAPTCHA solver...")
    print("Wait 1")
    await driver.sleep(3)

    recaptcha_iframe = await driver.find_element(By.XPATH, '//iframe[@title="reCAPTCHA"]', timeout=element_timeout)
    await driver.switch_to.frame(recaptcha_iframe)
    capcha_button = await driver.find_element(By.ID, "recaptcha-anchor-label", timeout=element_timeout)
    await capcha_button.click()

    print("Wait 2")
    await driver.sleep(3)

    second_url = "https://ship.pirateship.com/login/do"
    targets = await driver.targets
    temp_target = None
    for target_id, target_info in targets.items():
        url = await target_info.Target.current_url
        if second_url in url:
            title = target_info.title
            tar = await target_info.Target
            iframes = await tar.find_elements(By.TAG_NAME, "iframe")
            for index, iframe in enumerate(iframes):
                    if iframe != None:
                        src = await iframe.get_dom_attribute('src')
                        if src != None and "https://www.google.com/recaptcha/api2/bframe?hl=en&v=":
                            print(f"Iframe {index + 1}:")
                            print(f"  src: {src}")
                            print(f"  title: {await iframe.get_dom_attribute('title')}")
                            print(f"  name: {await iframe.get_dom_attribute('name')}")
                            print(f"  id: {await iframe.get_dom_attribute('id')}")
                            temp_target = iframe
                            break

    print("Found Captcha IFrame")

    second_recaptcha_iframe = await temp_target.find_element(By.XPATH, "//iframe[contains(@title, 'recaptcha challenge expires in two minutes')]", timeout=element_timeout)
    print("Wait 3")

    temp_fram = await second_recaptcha_iframe.content_document

    audio_button = await temp_fram.find_element(By.ID, 'recaptcha-audio-button', timeout=element_timeout) #"Get an audio challenge"
    await audio_button.click()

    print("Wait 4")
    await driver.sleep(3) #Waiting for //div[normalize-space()="Multiple correct solutions required - please solve more."]'

    try:
        download_link = await temp_fram.find_element(By.CLASS_NAME, 'rc-audiochallenge-tdownload-link', timeout=element_timeout)
        print("Got Download link")

    except:
        frame_check = await temp_fram.find_element(By.CLASS_NAME, 'rc-doscaptcha-body-text', timeout=element_timeout)
        frame_text = await frame_check.text
        print(frame_text)
        if frame_text in "Your computer or network may be sending automated queries. To protect our users, we can't process your request right now. For more details visit our help page.":
            print('Google has detected automated queries. Try again later.')
            await driver.sleep(2)
            return False

        print('Google has detected automated queries. Try again later.')
        await driver.sleep(2)
        return False


    link = await download_link.get_attribute('href')
    print("wait 5", link)
    text = await transcribe(link)

    solve_audio = await temp_fram.find_element(By.ID, "audio-response", timeout=element_timeout)
    await solve_audio.send_keys(text)

    verify_button = await temp_fram.find_element(By.ID, "recaptcha-verify-button", timeout=element_timeout)
    await verify_button.click()

    print("Completed reCAPTCHA")
    return True



async def main():
    options = webdriver.ChromeOptions()
    proxy = "*************:8080"

    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
    #C:\ProgramData\Microsoft\Windows\Start Menu\Programs
    async with webdriver.Chrome(options=options) as driver:
        
        #await driver.set_single_proxy(proxy)
        await driver.get('https://ship.pirateship.com/')
        
        email_input = await driver.find_element(By.CSS_SELECTOR, "input[name='email']", timeout=element_timeout)
        await email_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        
        await email_input.send_keys(email)
        await email_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)

                                                            
        password_input = await driver.find_element(By.CSS_SELECTOR, "input[name='password']", timeout=element_timeout)
        await password_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        
        await password_input.send_keys(password)

        # Click the login button to trigger Turnstile
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
        await login_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)

        # Wait a moment for Turnstile to load
        await driver.sleep(2)

        # Solve Cloudflare Turnstile using direct BotsForge integration
        turnstile_result = await cloudflare_turnstile_solver(driver)
        print("🎯 Turnstile solving result:", turnstile_result)

        if not turnstile_result:
            print("❌ Failed to solve Turnstile - attempting fallback to reCAPTCHA solver")
            try:
                # Fallback to reCAPTCHA solver in case site uses both
                cap_res = await recaptcha_solver_backup(driver)
                print("🔄 reCAPTCHA fallback result:", cap_res)
                if not cap_res:
                    print("❌ Both Turnstile and reCAPTCHA solving failed")
                    await driver.quit()
                    return
            except Exception as e:
                print(f"❌ Fallback reCAPTCHA solver failed: {e}")
                await driver.quit()
                return

        # If we reach here, either Turnstile or reCAPTCHA was solved successfully
        print("✅ CAPTCHA verification completed - proceeding with login")

        # Wait for login to complete and check for successful navigation
        await driver.sleep(3)

        # Check if we're successfully logged in by looking for dashboard elements
        try:
            current_url = await driver.current_url
            print(f"📍 Current URL after login: {current_url}")

            if "dashboard" in current_url or "ship" in current_url:
                print("🎉 Login successful - redirected to dashboard!")
            else:
                # Check if we're still on login page or got an error
                try:
                    error_element = await driver.find_element(By.CSS_SELECTOR, ".error, .alert-danger, [role='alert']", timeout=3)
                    error_text = await error_element.text
                    print(f"⚠️ Login error detected: {error_text}")
                except:
                    print("ℹ️ Login status unclear - continuing with automation")

        except Exception as e:
            print(f"⚠️ Could not verify login status: {e}")
            print("ℹ️ Continuing with automation...")
            
        print("huh")
        await driver.sleep(2)   
        #await driver.get('https://ship.pirateship.com/ship') # Need to go to default frame it keeps crashing here  !!!
        #await driver.switch_to.default_content() #maybe open new window or something idk
        #temp = await driver.get_screenshot_as_png()
        #await driver.refresh()
                

        # Locate the <a> element with class 'action-btn' and click it
        action_button = await driver.find_element(By.CLASS_NAME, "action-btn-label", timeout=element_timeout)
        await action_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        
        # Locate the <a> element with id 'copypaste-link' and click it
        paste_address_link = await driver.find_element(By.ID, "copypaste-link", timeout=element_timeout)
        await paste_address_link.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        
        textarea = await driver.find_element(By.ID, "shipment-copypaste", timeout=element_timeout)
        await  textarea.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)  # Click the textarea to focus
        await textarea.send_keys(address) 
        
        # Click and type the email
        email_input = await driver.find_element(By.ID, "shipment-email", timeout=element_timeout)
        await email_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await email_input.send_keys(send_email)

        # Click and type the phone number
        phone_input = await driver.find_element(By.ID, "shipment-phone", timeout=element_timeout)
        await phone_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await phone_input.send_keys(phone_number)

        # Click and type the recipient's name
        name_input = await driver.find_element(By.ID, "shipment-full-name", timeout=element_timeout)
        await name_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await name_input.send_keys(recipient_name)

        # Click and type the company name
        company_input = await driver.find_element(By.ID, "shipment-company", timeout=element_timeout)
        await company_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await company_input.send_keys(company_name)
        
        time.sleep(2)  # Adjust sleep time or use WebDriverWait for better handling

        # Select the specific option (for example, USPS Priority Mail Small Flat Rate Box)
        
        # Click and type the length
        length_input = await driver.find_element(By.ID, "configuration-key-length", timeout=element_timeout)
        await length_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=True,move_to=True)
        await length_input.send_keys(length)

        # Click and type the width
        width_input = await driver.find_element(By.ID, "configuration-key-width", timeout=element_timeout)
        await width_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await width_input.send_keys(width)

        # Click and type the height
        height_input = await driver.find_element(By.ID, "configuration-key-height", timeout=element_timeout)
        await height_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await height_input.send_keys(height)

        # Click and type the weight in pounds
        pounds_input = await driver.find_element(By.ID, "configuration-key-weight-pounds", timeout=element_timeout)
        await pounds_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await pounds_input.send_keys(pounds)

        # Check if the ounces input exists and interact with it
        try:
            ounces_input = await driver.find_element(By.ID, "configuration-key-weight-ounces", timeout=element_timeout)
            await ounces_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
            await ounces_input.send_keys(ounces)
        except:
            print("Ounces input field not found.")

        # Check if hazardous_materials is true and interact with the checkbox
        if hazardous_materials:
            hazardous_materials_checkbox = await driver.find_element(By.CSS_SELECTOR, "label.checkbox-label.checkbox input.toggle-checkbox.hazardous_materials_flag", timeout=element_timeout)
            await hazardous_materials_checkbox.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
            
        # Click the "Get Rates" button
        submit_button = await driver.find_element(By.XPATH, "//div[@class='col-md-12']//button[@id='origin-submit']", timeout=element_timeout)
        await submit_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=True,move_to=True)

        print("Clicked")
        time.sleep(2) 
        
        print("Best Selected by Default")
        
        # Locate and click the button
        buy_button = await driver.find_element(By.XPATH, "//button[@id='buy-button']", timeout=element_timeout)
        await buy_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=True,move_to=True)
        
        #From here you need to setup payment !!!
        print("Done")
        
        await driver.sleep(30) 
        await driver.quit()    

asyncio.run(main())


'''
async def click_checkbox(driver):
    await driver.switch_to.default_content()
    print("Yup")

    await driver.switch_to.frame(driver.find_element(By.XPATH, ".//iframe[@title='reCAPTCHA']"))
    print("Yup")

    capcha = await driver.find_element(By.CSS_SELECTOR, "#recaptcha-anchor > div.recaptcha-checkbox-border")
    await capcha.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
    print("Yup")
    
    await driver.switch_to.default_content()
    print("Yup")
    await driver.sleep(5)
    

async def request_audio_version(driver):
    await driver.switch_to.default_content()
    await driver.switch_to.frame(driver.find_element(By.XPATH, ".//iframe[@title='recaptcha challenge expires in two minutes']"))
    audio = await driver.find_element(By.ID, "recaptcha-audio-button", timeout=element_timeout)
    await audio.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)


async def solve_audio_captcha(driver):
    text = await transcribe(driver.find_element(By.ID, "audio-source").get_attribute('src'))
    solve_audio = await driver.find_element(By.ID, "audio-response", timeout=element_timeout)
    await solve_audio.send_keys(text)
    verify_button = await driver.find_element(By.ID, "recaptcha-verify-button", timeout=element_timeout)
    await verify_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)

async def captcha(driver):
    time.sleep(5)
    await click_checkbox(driver)
    time.sleep(5)
    await request_audio_version(driver)
    time.sleep(5)
    await solve_audio_captcha(driver)
    time.sleep(10)
            

'''
'''
        # Create temporary directory and temporary files
        tmp_dir = current_directory = os.getcwd()

        id_ = uuid.uuid4().hex

        mp3_file, wav_file = os.path.join(tmp_dir, f'{id_}_tmp.mp3'), os.path.join(tmp_dir, f'{id_}_tmp.wav')
        
        tmp_files = {mp3_file, wav_file}
                            
        print(tmp_files)
            # Convert MP3 to WAV format for compatibility with speech recognizer APIs
            AudioSegment.from_mp3(mp3_file).export(wav_file, format='wav')

            recognizer = sr.Recognizer()
            # Disable dynamic energy threshold to avoid failed reCAPTCHA audio transcription due to static noise
            recognizer.dynamic_energy_threshold = False

            with sr.AudioFile(wav_file) as source:
                audio = recognizer.listen(source)

                try:
                    recognized_text = _service.recognize(recognizer, audio, language)

                except sr.UnknownValueError:
                    raise RecaptchaException('Speech recognition API could not understand audio, try again')

            # Clean up all temporary files
            for path in tmp_files:
                if os.path.exists(path):
                    os.remove(path)
'''
    #except Exception as e:
        #print("Error occurred:", e)

    #finally:
    #   await driver.quit()
    
    
'''
puzzle_element = await driver.find_element(By.XPATH, '//*[@id="portal-game-toolbar"]/div/ul/div[2]/li[2]/ul/li[3]/button', timeout=element_timeout)
            await puzzle_element.click()
            
            #//*[@id="portal-game-modals"]/div/div/div[2]/article/div/button[2]
            reveal_two_element = await driver.find_element(By.XPATH, '//*[@id="portal-game-modals"]/div/div/div[2]/article/div/button[2]', timeout=element_timeout)
            await reveal_two_element.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)

            back_button_puzzle = await driver.find_element(By.CLASS_NAME, 'pz-icon pz-icon-close', timeout=element_timeout)
            await back_button_puzzle.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)

            #await driver.get('https://www.nytimes.com/crosswords/game/mini')

            #await driver.sleep(2.5)
            
            #button_element = await driver.find_element(By.CSS_SELECTOR, 'button.xwd__modal--subtle-button', timeout=element_timeout)
            #wait button_element.click() 

            #await driver.save_snapshot('test.mhtml')

            page_source = await driver.page_source

            #soup = BeautifulSoup(page_source, "html.parser")
            
            temp = await html_parser(BeautifulSoup(page_source, "html.parser"))
'''


# Iterate through each option and print its text and description
'''
option_text = "USPS Priority Mail Small Flat Rate Box"
dropdown_trigger = await driver.find_element(By.CSS_SELECTOR, "YOUR_DROPDOWN_TRIGGER_SELECTOR", timeout=element_timeout)
await driver.location_once_scrolled_into_view(dropdown_trigger)
await dropdown_trigger.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)

# Find the dropdown menu
dropdown_menu = await driver.find_element(By.CSS_SELECTOR, "ul.dd-options", timeout=element_timeout)
await dropdown_menu.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=True,move_to=False)

options = await dropdown_menu.find_elements(By.CSS_SELECTOR, "li a", timeout=element_timeout)
for option in options: 
    label = await option.find_element(By.CSS_SELECTOR, "label.dd-option-text", timeout=element_timeout).text #Might not work get rid of text 
    description = await option.find_element(By.CSS_SELECTOR, "small.dd-option-description", timeout=element_timeout).text
    print(f"Option: {label}")
    print(f"Description: {description}")
    print("-" * 40)

    # Check if this is the desired option and select it
    if label == option_text:
        await option.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=True,move_to=False)
        break
'''